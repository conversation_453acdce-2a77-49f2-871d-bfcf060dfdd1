# إضافة التحقق من رصيد الإجازات

## 🎯 الهدف من التعديل

إضافة التحقق من رصيد الإجازات المتاح قبل السماح بطلب إجازة جديدة، مع:

1. **التحقق من الرصيد** قبل إرسال الطلب
2. **عرض الرصيد المتاح** في شاشة طلب الإجازة
3. **منع الطلب** إذا لم يكن هناك رصيد كافٍ
4. **رسائل واضحة** للمستخدم عن حالة الرصيد

## 🐛 المشكلة الأصلية

### **النظام القديم:**
```
❌ لا يتحقق من رصيد الإجازات قبل الطلب
❌ المستخدم لا يرى رصيده المتاح
❌ يمكن طلب إجازة أكثر من الرصيد المتاح
❌ رسائل خطأ عامة وغير واضحة
```

### **المشاكل التقنية:**
```dart
// في leave_request_screen.dart - لا يتحقق من الرصيد
Future<void> _submitRequest() async {
  // مباشرة إلى إنشاء الطلب بدون تحقق من الرصيد
  final result = await widget.odooService.createLeaveRequest(
    uid: widget.uid,
    password: widget.password,
    leaveRequestData: leaveRequestData,
  );
  // ← لا يتحقق من الرصيد المتاح
}
```

## ✅ الحل المطبق

### **1. إضافة دالة للحصول على رصيد نوع إجازة محدد:**

#### **الدالة الجديدة في OdooService:**
```dart
/// دالة لجلب رصيد نوع إجازة محدد للموظف
Future<double?> getEmployeeLeaveBalanceForType({
  required int uid,
  required String password,
  required int leaveTypeId,
}) async {
  try {
    // الحصول على معرف الموظف
    final employeeId = await getEmployeeId(uid: uid, password: password);
    if (employeeId == null) return null;

    // جلب الرصيد المخصص لهذا النوع
    final allocationData = await executeKw(
      uid: uid,
      password: password,
      model: 'hr.leave.allocation',
      method: 'search_read',
      args: [
        [
          ['employee_id', '=', employeeId],
          ['holiday_status_id', '=', leaveTypeId],
          ['state', '=', 'validate'],
        ],
      ],
      kwargs: {'fields': ['number_of_days']},
    );

    // جلب الأيام المستخدمة لهذا النوع
    final usedDaysData = await executeKw(
      uid: uid,
      password: password,
      model: 'hr.leave',
      method: 'search_read',
      args: [
        [
          ['employee_id', '=', employeeId],
          ['holiday_status_id', '=', leaveTypeId],
          ['state', 'in', ['validate']],
        ],
      ],
      kwargs: {'fields': ['number_of_days']},
    );

    // حساب الرصيد المتاح = المخصص - المستخدم
    double totalAllocated = 0.0;
    double totalUsed = 0.0;

    if (allocationData != null && allocationData is List) {
      for (var allocation in allocationData) {
        totalAllocated += (allocation['number_of_days'] as num).toDouble();
      }
    }

    if (usedDaysData != null && usedDaysData is List) {
      for (var used in usedDaysData) {
        totalUsed += (used['number_of_days'] as num).toDouble();
      }
    }

    return totalAllocated - totalUsed; // ✅ الرصيد المتاح
  } catch (e) {
    return null;
  }
}
```

### **2. تحديث شاشة طلب الإجازة للتحقق من الرصيد:**

#### **قبل الإصلاح:**
```dart
Future<void> _submitRequest() async {
  // مباشرة إلى إنشاء الطلب
  final result = await widget.odooService.createLeaveRequest(
    uid: widget.uid,
    password: widget.password,
    leaveRequestData: leaveRequestData,
  );
  // ← لا يتحقق من الرصيد
}
```

#### **بعد الإصلاح:**
```dart
Future<void> _submitRequest() async {
  try {
    // 1. التحقق من رصيد الإجازات أولاً ✅
    final availableBalance = await widget.odooService
        .getEmployeeLeaveBalanceForType(
          uid: widget.uid,
          password: widget.password,
          leaveTypeId: widget.leaveType.id,
        );

    if (availableBalance == null) {
      // رسالة خطأ في التحقق من الرصيد
      _showSnackBar(
        'لا يمكن التحقق من رصيد الإجازات. يرجى المحاولة لاحقاً',
        Colors.orange,
      );
      return;
    }

    // 2. التحقق من كفاية الرصيد ✅
    if (availableBalance < _numberOfDays) {
      _showSnackBar(
        'لا يوجد لديك رصيد كافي من الإجازات\n'
        'الرصيد المتاح: ${availableBalance.toStringAsFixed(1)} يوم\n'
        'الأيام المطلوبة: ${_numberOfDays.toStringAsFixed(1)} يوم',
        Colors.red,
      );
      return; // ← منع الطلب
    }

    // 3. المتابعة لإنشاء الطلب فقط إذا كان الرصيد كافياً ✅
    final result = await widget.odooService.createLeaveRequest(
      uid: widget.uid,
      password: widget.password,
      leaveRequestData: leaveRequestData,
    );
    
    // معالجة النتيجة...
  } catch (e) {
    // معالجة الأخطاء...
  }
}
```

### **3. إضافة عرض الرصيد المتاح في الواجهة:**

#### **المتغيرات الجديدة:**
```dart
class _LeaveRequestScreenState extends State<LeaveRequestScreen> {
  // المتغيرات الموجودة...
  double? _availableBalance;     // ← الرصيد المتاح
  bool _isLoadingBalance = false; // ← حالة تحميل الرصيد
}
```

#### **دالة تحميل الرصيد:**
```dart
/// تحميل الرصيد المتاح لنوع الإجازة
Future<void> _loadAvailableBalance() async {
  setState(() {
    _isLoadingBalance = true;
  });

  try {
    final balance = await widget.odooService.getEmployeeLeaveBalanceForType(
      uid: widget.uid,
      password: widget.password,
      leaveTypeId: widget.leaveType.id,
    );

    if (mounted) {
      setState(() {
        _availableBalance = balance;
        _isLoadingBalance = false;
      });
    }
  } catch (e) {
    if (mounted) {
      setState(() {
        _availableBalance = null;
        _isLoadingBalance = false;
      });
    }
  }
}
```

#### **بطاقة عرض الرصيد:**
```dart
/// بناء بطاقة الرصيد المتاح
Widget _buildBalanceCard() {
  return Container(
    padding: const EdgeInsets.all(AppConfig.largeSpacing),
    decoration: BoxDecoration(
      gradient: const LinearGradient(
        colors: [Color(0xFFE3F2FD), Color(0xFFBBDEFB)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
      boxShadow: [
        BoxShadow(
          color: const Color(0xFF2196F3).withValues(alpha: 0.2),
          blurRadius: 20,
          offset: const Offset(0, 8),
        ),
      ],
    ),
    child: Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF2196F3).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.account_balance_wallet,
            color: Color(0xFF1976D2),
            size: 28,
          ),
        ),
        const SizedBox(width: AppConfig.spacing),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الرصيد المتاح',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: const Color(0xFF1976D2),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              if (_isLoadingBalance)
                const CircularProgressIndicator(strokeWidth: 2)
              else if (_availableBalance != null)
                Text(
                  '${_availableBalance!.toStringAsFixed(1)} يوم',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: const Color(0xFF1976D2),
                    fontWeight: FontWeight.w600,
                  ),
                )
              else
                Text(
                  'غير متاح',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.orange,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
        ),
      ],
    ),
  );
}
```

## 🎯 النتائج المحققة

### **النظام الجديد:**
```
✅ يتحقق من رصيد الإجازات قبل الطلب
✅ يعرض الرصيد المتاح للمستخدم
✅ يمنع الطلب إذا لم يكن هناك رصيد كافٍ
✅ رسائل واضحة ومفصلة للمستخدم
✅ تجربة مستخدم محسنة
```

### **تدفق العمل الجديد:**

#### **عند فتح شاشة طلب الإجازة:**
```
1. تحميل الشاشة → _loadAvailableBalance()
2. جلب الرصيد المتاح → من Odoo
3. عرض الرصيد → في بطاقة مخصصة
4. المستخدم يرى رصيده → قبل طلب الإجازة ✅
```

#### **عند طلب إجازة:**
```
1. المستخدم يحدد التواريخ → حساب عدد الأيام
2. ينقر على "تقديم الطلب" → _submitRequest()
3. التحقق من الرصيد أولاً → getEmployeeLeaveBalanceForType()
4. مقارنة الرصيد مع الأيام المطلوبة:
   - إذا كان الرصيد كافياً → متابعة الطلب ✅
   - إذا لم يكن كافياً → منع الطلب + رسالة واضحة ❌
```

## 📊 أمثلة الرسائل الجديدة

### **رسالة عدم كفاية الرصيد:**
```
❌ لا يوجد لديك رصيد كافي من الإجازات
الرصيد المتاح: 5.0 يوم
الأيام المطلوبة: 7.0 يوم
```

### **رسالة خطأ في التحقق:**
```
⚠️ لا يمكن التحقق من رصيد الإجازات. يرجى المحاولة لاحقاً
```

### **عرض الرصيد في البطاقة:**
```
💰 الرصيد المتاح
   12.5 يوم
```

## 🔧 التفاصيل التقنية

### **حساب الرصيد:**
```
الرصيد المتاح = الأيام المخصصة - الأيام المستخدمة

الأيام المخصصة: من hr.leave.allocation (state = 'validate')
الأيام المستخدمة: من hr.leave (state = 'validate')
```

### **التحقق من الرصيد:**
```dart
if (availableBalance < _numberOfDays) {
  // منع الطلب + رسالة واضحة
  return;
}
// المتابعة للطلب
```

### **عرض الرصيد:**
```dart
// تحميل عند فتح الشاشة
@override
void initState() {
  super.initState();
  _initAnimations();
  _loadAvailableBalance(); // ← تحميل الرصيد
}

// عرض في الواجهة
_buildBalanceCard() // ← بطاقة الرصيد
```

## 🎨 التحسينات المضافة

### **1. واجهة مستخدم محسنة:**
- 💰 **بطاقة الرصيد** - عرض واضح ومرئي
- 🎨 **تصميم متسق** - مع باقي البطاقات
- ⏳ **مؤشر تحميل** - أثناء جلب الرصيد
- 🎯 **رسائل واضحة** - للحالات المختلفة

### **2. منطق محسن:**
- ✅ **تحقق مسبق** - قبل إرسال الطلب
- 🔍 **تحقق دقيق** - لنوع الإجازة المحدد
- 🛡️ **منع الأخطاء** - قبل وصولها للخادم
- 📊 **حساب دقيق** - للرصيد المتاح

### **3. تجربة مستخدم ممتازة:**
- 👁️ **شفافية كاملة** - المستخدم يرى رصيده
- 🚫 **منع الإحباط** - لا طلبات فاشلة
- 💬 **رسائل مفيدة** - تشرح المشكلة بوضوح
- ⚡ **استجابة سريعة** - تحقق فوري

## 📁 الملفات المحدثة

### **الملفات الرئيسية:**
- ✅ `lib/services/odoo_service.dart` - إضافة `getEmployeeLeaveBalanceForType()`
- ✅ `lib/screens/leave_request_screen.dart` - إضافة التحقق من الرصيد وعرضه

### **الوظائف المضافة:**
- ✅ `getEmployeeLeaveBalanceForType()` - جلب رصيد نوع إجازة محدد
- ✅ `_loadAvailableBalance()` - تحميل الرصيد في الشاشة
- ✅ `_buildBalanceCard()` - عرض بطاقة الرصيد

### **النتائج:**
- ✅ **84 اختبار ناجح** - جميع الاختبارات تعمل
- ✅ **تحقق من الرصيد** - قبل كل طلب إجازة
- ✅ **عرض الرصيد** - واضح ومرئي للمستخدم
- ✅ **منع الطلبات الخاطئة** - حماية من عدم كفاية الرصيد

---

**تم إضافة التحقق من رصيد الإجازات بنجاح! الآن المستخدم يرى رصيده المتاح ولا يمكنه طلب إجازة أكثر من رصيده 💰✨**

**النظام الآن يحمي من الطلبات الخاطئة مع تجربة مستخدم واضحة ومفيدة! 🛡️🎯**
