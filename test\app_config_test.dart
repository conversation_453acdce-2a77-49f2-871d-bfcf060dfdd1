import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/config/app_config.dart';

void main() {
  group('AppConfig Tests', () {
    setUp(() async {
      // تهيئة AppConfig قبل كل اختبار
      await AppConfig.initialize();
    });

    test('should initialize AppConfig successfully', () async {
      // تنفيذ
      await AppConfig.initialize();

      // تحقق - لا يجب أن يحدث خطأ
      expect(true, isTrue);
    });

    test('should get server URL from environment', () {
      // تنفيذ
      final serverUrl = AppConfig.defaultServerUrl;

      // تحقق
      expect(serverUrl, isNotEmpty);
      expect(serverUrl, contains('http'));
    });

    test('should get database from environment', () {
      // تنفيذ
      final database = AppConfig.defaultDatabase;

      // تحقق
      expect(database, isNotEmpty);
    });

    test('should get API key', () {
      // تنفيذ
      final apiKey = AppConfig.apiKey;

      // تحقق - يجب أن يكون string (قد يكون فارغاً)
      expect(apiKey, isA<String>());
    });

    test('should validate configuration', () {
      // تنفيذ
      final isValid = AppConfig.isConfigurationValid;

      // تحقق
      expect(isValid, isTrue);
    });

    test('should get API key', () {
      // تنفيذ
      final apiKey = AppConfig.apiKey;

      // تحقق - يجب أن يكون string (قد يكون فارغاً)
      expect(apiKey, isA<String>());
    });

    test('should have correct app information', () {
      // تحقق من المعلومات الثابتة للتطبيق
      expect(AppConfig.appName, equals('بنك الموظفين'));
      expect(AppConfig.appSubtitle, equals('نظام إدارة الموظفين المصرفي'));
      expect(AppConfig.appVersion, equals('1.0.0'));
    });

    test('should have correct timeout settings', () {
      // تحقق من إعدادات المهلة الزمنية
      expect(AppConfig.connectionTimeout, equals(30));
      expect(AppConfig.requestTimeout, equals(15));
    });

    test('should have correct error messages', () {
      // تحقق من رسائل الخطأ
      expect(AppConfig.connectionErrorMessage, isNotEmpty);
      expect(AppConfig.authenticationErrorMessage, isNotEmpty);
      expect(AppConfig.noEmployeeDataMessage, isNotEmpty);
      expect(AppConfig.generalErrorMessage, isNotEmpty);
    });

    test('should have correct color constants', () {
      // تحقق من الألوان
      expect(AppConfig.primaryColor, equals(0xFF3F91EB));
      expect(AppConfig.whiteColor, equals(0xFFFFFFFF));
      expect(AppConfig.successColor, equals(0xFF28C76F));
      expect(AppConfig.errorColor, equals(0xFFEA5455));
    });

    test('should have correct design dimensions', () {
      // تحقق من أبعاد التصميم
      expect(AppConfig.borderRadius, equals(16.0));
      expect(AppConfig.largeBorderRadius, equals(24.0));
      expect(AppConfig.cardElevation, equals(4.0));
      expect(AppConfig.buttonHeight, equals(56.0));
      expect(AppConfig.spacing, equals(16.0));
    });

    test('should have correct font sizes', () {
      // تحقق من أحجام الخطوط
      expect(AppConfig.headlineFontSize, equals(28.0));
      expect(AppConfig.titleFontSize, equals(22.0));
      expect(AppConfig.bodyFontSize, equals(16.0));
      expect(AppConfig.captionFontSize, equals(14.0));
      expect(AppConfig.smallFontSize, equals(12.0));
    });

    test('should reload configuration', () async {
      // ترتيب
      final originalUrl = AppConfig.defaultServerUrl;

      // تنفيذ
      await AppConfig.reload();
      final newUrl = AppConfig.defaultServerUrl;

      // تحقق - يجب أن تعمل بعد إعادة التحميل
      expect(newUrl, equals(originalUrl));
    });

    test('should handle configuration validation', () {
      // تنفيذ
      final isValid = AppConfig.isConfigurationValid;

      // تحقق
      expect(isValid, isA<bool>());
    });

    test('should handle multiple initializations gracefully', () async {
      // تنفيذ - تهيئة متعددة
      await AppConfig.initialize();
      await AppConfig.initialize();
      await AppConfig.initialize();

      // تحقق - يجب أن تعمل بدون مشاكل
      final serverUrl = AppConfig.defaultServerUrl;
      expect(serverUrl, isNotEmpty);
    });
  });
}
