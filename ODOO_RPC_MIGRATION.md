# تحديث OdooService لاستخدام مكتبة odoo_rpc

## نظرة عامة

تم تحديث ملف `lib/services/odoo_service.dart` لاستخدام مكتبة `odoo_rpc` بدلاً من `http` مباشرة، مع الحفاظ على نفس الواجهة والوظائف.

## التغييرات المطبقة

### 1. إضافة المكتبة الجديدة

```yaml
# في pubspec.yaml
dependencies:
  # Odoo RPC client
  odoo_rpc: ^0.7.1
```

### 2. تحديث الاستيرادات

**قبل:**
```dart
import 'dart:convert';
import 'package:http/http.dart' as http;
```

**بعد:**
```dart
import 'package:odoo_rpc/odoo_rpc.dart';
```

### 3. تحديث بنية الكلاس

**قبل:**
```dart
class OdooService {
  final String baseUrl;
  final String database;
  
  OdooService({required this.baseUrl, required this.database});
}
```

**بعد:**
```dart
class OdooService {
  final String baseUrl;
  final String database;
  late final OdooClient _client;
  OdooSession? _session;

  OdooService({required this.baseUrl, required this.database}) {
    _client = OdooClient(baseUrl);
  }
}
```

### 4. تحديث دالة المصادقة

**قبل:**
```dart
Future<int?> authenticate(String email, String password) async {
  try {
    final url = Uri.parse('$baseUrl/jsonrpc');
    final requestBody = {
      'jsonrpc': '2.0',
      'method': 'call',
      'params': {
        'service': 'common',
        'method': 'authenticate',
        'args': [database, email, password, {}],
      },
      'id': 1,
    };
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: json.encode(requestBody),
    ).timeout(Duration(seconds: AppConfig.requestTimeout));
    
    // معالجة الاستجابة...
  } catch (e) {
    return null;
  }
}
```

**بعد:**
```dart
Future<int?> authenticate(String email, String password) async {
  try {
    _session = await _client.authenticate(database, email, password);
    return _session?.userId;
  } catch (e) {
    debugPrint('خطأ في المصادقة: $e');
    return null;
  }
}
```

### 5. تحديث دالة executeKw

**قبل:**
```dart
Future<dynamic> executeKw({...}) async {
  try {
    final url = Uri.parse('$baseUrl/jsonrpc');
    final requestBody = {
      'jsonrpc': '2.0',
      'method': 'call',
      'params': {
        'service': 'object',
        'method': 'execute_kw',
        'args': [database, uid, password, model, method, args, kwargs],
      },
      'id': 1,
    };
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: json.encode(requestBody),
    );
    
    // معالجة الاستجابة...
  } catch (e) {
    return null;
  }
}
```

**بعد:**
```dart
Future<dynamic> executeKw({...}) async {
  try {
    // إذا لم تكن هناك جلسة، أنشئ واحدة جديدة
    if (_session == null || _session!.userId != uid) {
      _session = await _client.authenticate(database, password, password);
    }

    final result = await _client.callKw({
      'model': model,
      'method': method,
      'args': args,
      'kwargs': kwargs,
    });

    return result;
  } catch (e) {
    debugPrint('خطأ في executeKw: $e');
    return null;
  }
}
```

## المزايا الجديدة

### 1. **تبسيط الكود**
- إزالة الحاجة لبناء JSON-RPC requests يدوياً
- معالجة أفضل للأخطاء والاستثناءات
- كود أكثر قابلية للقراءة والصيانة

### 2. **إدارة الجلسات**
- إدارة تلقائية لجلسات Odoo
- تتبع تغييرات الجلسة عبر Stream
- معالجة انتهاء صلاحية الجلسة

### 3. **أداء محسن**
- إعادة استخدام الجلسات
- تقليل عدد طلبات المصادقة
- معالجة أفضل للاتصالات

### 4. **دعم أفضل للمنصات**
- دعم محسن لمنصة الويب
- معالجة CORS بشكل أفضل
- دعم BrowserClient للتطبيقات الويب

## التوافق

- ✅ **الواجهة البرمجية**: لم تتغير - جميع الدوال تعمل بنفس الطريقة
- ✅ **القيم المرجعة**: نفس أنواع البيانات المرجعة
- ✅ **معالجة الأخطاء**: نفس السلوك في حالة الأخطاء
- ✅ **الاختبارات**: جميع الاختبارات الموجودة تعمل بنجاح

## الاختبار

تم إنشاء اختبارات جديدة في `test/odoo_service_rpc_test.dart` للتأكد من:
- إنشاء instance بشكل صحيح
- معالجة بيانات اعتماد غير صحيحة
- التعامل مع معاملات غير صالحة

```bash
flutter test test/odoo_service_rpc_test.dart
```

## الخلاصة

التحديث تم بنجاح مع الحفاظ على:
- ✅ نفس الواجهة البرمجية
- ✅ نفس الوظائف
- ✅ نفس السلوك
- ✅ تحسينات في الأداء والصيانة
