# مثال على إعداد Odoo للتطبيق

## إعداد خادم Odoo

### 1. تثبيت Odoo 15
```bash
# على Ubuntu/Debian
sudo apt update
sudo apt install odoo

# أو باستخدام Docker
docker run -d -e POSTGRES_USER=odoo -e POSTGRES_PASSWORD=odoo -e POSTGRES_DB=postgres --name db postgres:13
docker run -p 8069:8069 --name odoo --link db:db -t odoo:15.0
```

### 2. إنشاء قاعدة بيانات
- افتح المتصفح على `http://localhost:8069`
- أنشئ قاعدة بيانات جديدة (مثل: `odoo_test`)
- قم بتثبيت موديل Human Resources

### 3. إنشاء مستخدم للاختبار
```python
# في Odoo shell أو من خلال الواجهة
user = self.env['res.users'].create({
    'name': 'Test Employee',
    'login': '<EMAIL>',
    'password': 'test123',
    'groups_id': [(6, 0, [self.env.ref('base.group_user').id])]
})

# إنشاء سجل موظف مرتبط بالمستخدم
employee = self.env['hr.employee'].create({
    'name': 'Test Employee',
    'user_id': user.id,
    'work_email': '<EMAIL>',
})
```

## إعدادات التطبيق

### بيانات تسجيل الدخول للاختبار:
- **رابط الخادم**: `http://localhost:8069`
- **قاعدة البيانات**: `odoo_test`
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `test123`

### إعداد المدير المباشر (اختياري):
```python
# إنشاء مدير
manager_user = self.env['res.users'].create({
    'name': 'Manager User',
    'login': '<EMAIL>',
    'password': 'manager123',
})

manager_employee = self.env['hr.employee'].create({
    'name': 'Manager User',
    'user_id': manager_user.id,
    'work_email': '<EMAIL>',
})

# ربط الموظف بالمدير
employee.write({'parent_id': manager_employee.id})
```

## استكشاف الأخطاء

### مشكلة: "Access Denied"
```python
# إضافة صلاحيات للمستخدم
user.groups_id = [(6, 0, [
    self.env.ref('base.group_user').id,
    self.env.ref('hr.group_hr_user').id,
])]
```

### مشكلة: "لا توجد بيانات موظف"
- تأكد من ربط المستخدم بسجل في `hr.employee`
- تحقق من أن `user_id` في سجل الموظف يطابق UID المستخدم

### مشكلة: CORS في المتصفح
```python
# في ملف إعدادات Odoo
[options]
proxy_mode = True
```
