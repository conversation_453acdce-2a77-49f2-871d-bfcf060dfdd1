# الإعدادات الآمنة - دليل الإعداد والاستخدام

## 🔒 نظرة عامة

تم تطوير نظام إعدادات آمن مبسط لحماية المعلومات الحساسة مثل عناوين الخوادم وأسماء قواعد البيانات من التعرض في الكود المصدري.

## ✅ الميزات الجديدة

### 🛡️ **الأمان المحسن:**
- **إخفاء المعلومات الحساسة** من الكود المصدري
- **استخدام متغيرات البيئة** للإعدادات الحساسة
- **ملفات إعدادات منفصلة** غير مدرجة في Git
- **نظام مبسط وآمن** للإنتاج

### 🔧 **سهولة الإدارة:**
- **تهيئة تلقائية** عند بدء التطبيق
- **إعدادات افتراضية** قابلة للتخصيص
- **تشخيص وتحقق** من صحة الإعدادات
- **إعادة تحميل ديناميكية** للإعدادات

## 📋 طرق الإعداد

### 1. 🌍 **متغيرات البيئة (الطريقة المفضلة)**

#### **للتطوير المحلي:**
```bash
# Windows
set ODOO_SERVER_URL=http://localhost:8069
set ODOO_DATABASE=my_dev_db
set APP_ENVIRONMENT=development

# Linux/Mac
export ODOO_SERVER_URL=http://localhost:8069
export ODOO_DATABASE=my_dev_db
export APP_ENVIRONMENT=development
```

#### **للإنتاج:**
```bash
export ODOO_SERVER_URL=https://your-production-server.com
export ODOO_DATABASE=production_db
export ODOO_API_KEY=your_secure_api_key
export APP_ENVIRONMENT=production
```

### 2. 📄 **ملف .env (للتطوير)**

انسخ `.env.example` إلى `.env` وعدل القيم:

```bash
cp .env.example .env
```

محتوى ملف `.env`:
```env
# إعدادات خادم Odoo
ODOO_SERVER_URL=http://*************:8069
ODOO_DATABASE=my_company_db

# مفتاح API (اختياري)
ODOO_API_KEY=

# بيئة التشغيل
APP_ENVIRONMENT=development
```

### 3. 📊 **ملف JSON (متقدم)**

انسخ `config/app_config.json.example` إلى `config/app_config.json`:

```bash
cp config/app_config.json.example config/app_config.json
```

عدل الإعدادات حسب بيئتك:
```json
{
  "development": {
    "serverUrl": "http://localhost:8069",
    "database": "odoo_dev",
    "environment": "development"
  },
  "production": {
    "serverUrl": "https://your-server.com",
    "database": "production_db",
    "environment": "production"
  }
}
```

## 🚀 الاستخدام

### **في الكود:**
```dart
// تهيئة الإعدادات (تتم تلقائياً في main.dart)
await AppConfig.initialize();

// الحصول على الإعدادات
final serverUrl = AppConfig.defaultServerUrl;
final database = AppConfig.defaultDatabase;

// التحقق من البيئة
if (AppConfig.isDevelopment) {
  print('وضع التطوير مفعل');
}

// التحقق من صحة الإعدادات
if (AppConfig.isConfigurationValid) {
  print('الإعدادات صحيحة ✅');
}
```

### **معلومات التشخيص:**
```dart
// في بيئة التطوير فقط
final diagnostics = AppConfig.getDiagnosticInfo();
print('حالة الإعدادات: $diagnostics');
```

## 🔧 أولوية الإعدادات

النظام يقرأ الإعدادات بالترتيب التالي:

1. **متغيرات البيئة** (أولوية عالية)
2. **ملف .env** (أولوية متوسطة)
3. **ملف config.json** (أولوية منخفضة)
4. **القيم الافتراضية** (احتياطية)

## 📁 الملفات المهمة

### **ملفات الإعدادات:**
- `.env` - إعدادات التطوير المحلية (مخفي من Git)
- `.env.example` - مثال على ملف .env
- `config/app_config.json` - إعدادات JSON (مخفي من Git)
- `config/app_config.json.example` - مثال على ملف JSON

### **ملفات الكود:**
- `lib/services/environment_service.dart` - خدمة إدارة البيئة
- `lib/config/app_config.dart` - إعدادات التطبيق المحدثة

## 🛡️ الأمان

### **ما يُحفظ في Git:**
- ✅ ملفات المثال (`.example`)
- ✅ الكود المصدري بدون معلومات حساسة
- ✅ الإعدادات العامة والافتراضية

### **ما لا يُحفظ في Git:**
- ❌ ملف `.env`
- ❌ ملف `config/app_config.json`
- ❌ أي ملفات تحتوي على معلومات حساسة

### **الحماية المطبقة:**
- 🔒 إخفاء عناوين الخوادم الحقيقية
- 🔒 حماية أسماء قواعد البيانات
- 🔒 إخفاء مفاتيح API
- 🔒 فصل إعدادات البيئات المختلفة

## 🚨 استكشاف الأخطاء

### **مشكلة: "خدمة البيئة غير مهيأة"**
```dart
// الحل: تأكد من استدعاء التهيئة
await AppConfig.initialize();
```

### **مشكلة: "الإعدادات غير صحيحة"**
```dart
// التحقق من الإعدادات
final status = AppConfig.getConfigurationStatus();
print(status);

// معلومات التشخيص
final info = AppConfig.getDiagnosticInfo();
print(info);
```

### **مشكلة: "لا يمكن الاتصال بالخادم"**
1. تحقق من متغيرات البيئة
2. تأكد من صحة عنوان الخادم
3. تحقق من ملف `.env`

## 📝 أمثلة للبيئات المختلفة

### **التطوير المحلي:**
```env
ODOO_SERVER_URL=http://localhost:8069
ODOO_DATABASE=odoo_dev
APP_ENVIRONMENT=development
```

### **خادم الاختبار:**
```env
ODOO_SERVER_URL=http://test-server:8069
ODOO_DATABASE=odoo_test
APP_ENVIRONMENT=testing
```

### **الإنتاج:**
```env
ODOO_SERVER_URL=https://secure-server.company.com
ODOO_DATABASE=company_production
ODOO_API_KEY=secure_api_key_here
APP_ENVIRONMENT=production
```

## 🔄 الترقية من النظام القديم

### **الخطوات:**
1. **انسخ الإعدادات الحالية** من `app_config.dart`
2. **أنشئ ملف `.env`** بالإعدادات الجديدة
3. **احذف الإعدادات القديمة** من الكود (تم بالفعل)
4. **اختبر التطبيق** للتأكد من عمله

### **مثال على النقل:**
```dart
// القديم (في app_config.dart)
static const String defaultServerUrl = 'http://************:8069';
static const String defaultDatabase = 'BSIC-Data';

// الجديد (في .env)
ODOO_SERVER_URL=http://************:8069
ODOO_DATABASE=BSIC-Data
```

## 🎯 الفوائد

### **للمطورين:**
- 🔧 إعداد سهل ومرن
- 🔄 تبديل سريع بين البيئات
- 🛡️ حماية المعلومات الحساسة
- 📊 تشخيص وتتبع الإعدادات

### **للأمان:**
- 🔒 عدم تعرض المعلومات الحساسة
- 🌍 إعدادات مختلفة لكل بيئة
- 🔐 حماية من التسريب في Git
- 🛡️ توافق مع معايير الأمان

---

## 📞 الدعم

للمساعدة في الإعداد:
1. راجع ملفات المثال (`.example`)
2. استخدم `AppConfig.getDiagnosticInfo()` للتشخيص
3. تحقق من `AppConfig.getConfigurationStatus()`

**النظام الجديد يوفر أماناً أفضل ومرونة أكبر! 🚀**
