import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:odoo_employee_app/services/storage_service.dart';
import 'package:odoo_employee_app/services/secure_storage_service.dart';

void main() {
  group('StorageService Tests', () {
    setUp(() async {
      // تنظيف البيانات قبل كل اختبار
      SharedPreferences.setMockInitialValues({});

      // تهيئة خدمة التشفير للاختبارات
      try {
        await SecureStorageService.initialize();
      } catch (e) {
        // تجاهل أخطاء التهيئة في بيئة الاختبار
      }
    });

    test('should save and retrieve login credentials', () async {
      // ترتيب
      const email = '<EMAIL>';
      const password = 'testpassword';
      const rememberMe = true;
      const serverUrl = 'http://localhost:8069';
      const database = 'test_db';

      // تنفيذ
      await StorageService.saveLoginCredentials(
        email: email,
        password: password,
        rememberMe: rememberMe,
        serverUrl: serverUrl,
        database: database,
      );

      final credentials = await StorageService.getLoginCredentials();

      // تحقق
      expect(credentials['email'], equals(email));
      expect(credentials['password'], equals(password));
      expect(credentials['rememberMe'], equals('true'));
      expect(credentials['serverUrl'], equals(serverUrl));
      expect(credentials['database'], equals(database));
    });

    test('should return null credentials when rememberMe is false', () async {
      // ترتيب
      const email = '<EMAIL>';
      const password = 'testpassword';
      const rememberMe = false;

      // تنفيذ
      await StorageService.saveLoginCredentials(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      final credentials = await StorageService.getLoginCredentials();

      // تحقق
      expect(credentials['email'], isNull);
      expect(credentials['password'], isNull);
      expect(credentials['rememberMe'], equals('false'));
    });

    test('should check if credentials are remembered', () async {
      // ترتيب
      const email = '<EMAIL>';
      const password = 'testpassword';

      // تنفيذ - حفظ مع تذكر
      await StorageService.saveLoginCredentials(
        email: email,
        password: password,
        rememberMe: true,
      );

      bool hasRemembered = await StorageService.hasRememberedCredentials();

      // تحقق
      expect(hasRemembered, isTrue);

      // تنفيذ - حفظ بدون تذكر
      await StorageService.saveLoginCredentials(
        email: email,
        password: password,
        rememberMe: false,
      );

      hasRemembered = await StorageService.hasRememberedCredentials();

      // تحقق
      expect(hasRemembered, isFalse);
    });

    test('should clear login credentials', () async {
      // ترتيب
      await StorageService.saveLoginCredentials(
        email: '<EMAIL>',
        password: 'testpassword',
        rememberMe: true,
      );

      // تنفيذ
      await StorageService.clearLoginCredentials();

      final credentials = await StorageService.getLoginCredentials();
      final hasRemembered = await StorageService.hasRememberedCredentials();

      // تحقق
      expect(credentials['email'], isNull);
      expect(credentials['password'], isNull);
      expect(credentials['rememberMe'], equals('false'));
      expect(hasRemembered, isFalse);
    });

    test('should save and retrieve server settings', () async {
      // ترتيب
      const serverUrl = 'http://localhost:8069';
      const database = 'test_database';

      // تنفيذ
      await StorageService.saveServerSettings(
        serverUrl: serverUrl,
        database: database,
      );

      final settings = await StorageService.getServerSettings();

      // تحقق
      expect(settings['serverUrl'], equals(serverUrl));
      expect(settings['database'], equals(database));
    });

    test('should save email only', () async {
      // ترتيب
      const email = '<EMAIL>';

      // تنفيذ
      await StorageService.saveEmailOnly(email);
      final savedEmail = await StorageService.getSavedEmail();

      // تحقق
      expect(savedEmail, equals(email));
    });

    group('Encryption Tests', () {
      test('should encrypt and decrypt passwords correctly', () async {
        // ترتيب
        const email = '<EMAIL>';
        const password = 'securePassword123!@#';
        const rememberMe = true;

        // تنفيذ - حفظ مع التشفير
        await StorageService.saveLoginCredentials(
          email: email,
          password: password,
          rememberMe: rememberMe,
        );

        // استرجاع البيانات
        final credentials = await StorageService.getLoginCredentials();

        // تحقق - كلمة المرور يجب أن تكون مفكوكة التشفير وصحيحة
        expect(credentials['email'], equals(email));
        expect(credentials['password'], equals(password));
        expect(credentials['rememberMe'], equals('true'));
      });

      test('should handle encryption errors gracefully', () async {
        // ترتيب - محاولة استرجاع بيانات غير موجودة
        final credentials = await StorageService.getLoginCredentials();

        // تحقق - يجب أن ترجع بيانات فارغة بدلاً من خطأ
        expect(credentials['email'], isNull);
        expect(credentials['password'], isNull);
        expect(credentials['rememberMe'], equals('false'));
      });

      test('should migrate old unencrypted credentials', () async {
        // ترتيب - محاكاة بيانات قديمة غير مشفرة
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_password', 'oldUnencryptedPassword');

        // تنفيذ
        final migrated = await StorageService.migrateOldCredentials();

        // تحقق
        expect(migrated, isTrue);

        // التحقق من عدم وجود كلمة المرور القديمة
        final oldPassword = prefs.getString('user_password');
        expect(oldPassword, isNull);
      });

      test('should clear all encrypted data', () async {
        // ترتيب - حفظ بيانات مشفرة
        await StorageService.saveLoginCredentials(
          email: '<EMAIL>',
          password: 'testPassword',
          rememberMe: true,
        );

        // تأكيد وجود البيانات
        bool hasCredentials = await StorageService.hasRememberedCredentials();
        expect(hasCredentials, isTrue);

        // تنفيذ - مسح جميع البيانات
        await StorageService.clearAllData();

        // تحقق - عدم وجود بيانات
        hasCredentials = await StorageService.hasRememberedCredentials();
        expect(hasCredentials, isFalse);
      });
    });
  });
}
