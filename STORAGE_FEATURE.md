# ميزة حفظ بيانات تسجيل الدخول

## نظرة عامة

تم إضافة ميزة جديدة لحفظ بيانات تسجيل الدخول محلياً على الجهاز، مما يوفر تجربة مستخدم محسنة ويقلل من الحاجة لإدخال البيانات في كل مرة.

## الميزات الجديدة

### 1. خدمة التخزين المحلي (`StorageService`)
- **الموقع**: `lib/services/storage_service.dart`
- **الوظيفة**: إدارة حفظ واسترجاع بيانات المستخدم بشكل آمن
- **التقنية**: استخدام مكتبة `shared_preferences`

### 2. واجهة المستخدم المحدثة
- **خانة اختيار "تذكرني"** في شاشة تسجيل الدخول
- **أيقونة معلومات** لشرح الميزة للمستخدم
- **نافذة تأكيد** عند تسجيل الخروج مع خيارات متعددة

### 3. إدارة ذكية للبيانات
- **حفظ تلقائي** عند تفعيل "تذكرني"
- **تحميل تلقائي** للبيانات المحفوظة عند فتح التطبيق
- **مسح اختياري** للبيانات عند تسجيل الخروج

## كيفية الاستخدام

### للمستخدم النهائي:

1. **تسجيل الدخول لأول مرة**:
   ```
   - أدخل البريد الإلكتروني وكلمة المرور
   - فعّل خيار "تذكر بيانات تسجيل الدخول"
   - اضغط على تسجيل الدخول
   ```

2. **الدخول في المرات التالية**:
   ```
   - ستجد البيانات محفوظة تلقائياً
   - يمكنك تعديل البيانات أو إلغاء "تذكرني"
   ```

3. **تسجيل الخروج**:
   ```
   - اختر "الخروج مع الاحتفاظ بالبيانات" للحفظ
   - أو "مسح البيانات والخروج" للحذف
   ```

### للمطورين:

#### استخدام خدمة التخزين:

```dart
// حفظ بيانات تسجيل الدخول
await StorageService.saveLoginCredentials(
  email: '<EMAIL>',
  password: 'password123',
  rememberMe: true,
  serverUrl: 'http://localhost:8069',
  database: 'my_database',
);

// استرجاع البيانات المحفوظة
final credentials = await StorageService.getLoginCredentials();
String? email = credentials['email'];
String? password = credentials['password'];

// التحقق من وجود بيانات محفوظة
bool hasCredentials = await StorageService.hasRememberedCredentials();

// مسح البيانات المحفوظة
await StorageService.clearLoginCredentials();
```

#### إضافة الميزة لشاشات أخرى:

```dart
// في initState()
@override
void initState() {
  super.initState();
  _loadSavedCredentials();
}

// دالة تحميل البيانات المحفوظة
Future<void> _loadSavedCredentials() async {
  final credentials = await StorageService.getLoginCredentials();
  if (credentials['rememberMe'] == 'true') {
    setState(() {
      _emailController.text = credentials['email'] ?? '';
      _passwordController.text = credentials['password'] ?? '';
      _rememberMe = true;
    });
  }
}
```

## الأمان والخصوصية

### التشفير:
- البيانات محفوظة باستخدام `shared_preferences`
- التشفير على مستوى نظام التشغيل
- لا يتم إرسال البيانات خارج الجهاز

### التحكم:
- المستخدم يتحكم كاملاً في حفظ/مسح البيانات
- خيارات واضحة ومفهومة
- إمكانية المسح في أي وقت

### الشفافية:
- رسائل توضيحية للمستخدم
- أيقونة معلومات لشرح الميزة
- تأكيد قبل المسح

## الاختبارات

تم إنشاء مجموعة شاملة من الاختبارات في `test/storage_service_test.dart`:

```bash
# تشغيل اختبارات خدمة التخزين
flutter test test/storage_service_test.dart

# تشغيل جميع الاختبارات
flutter test
```

## التحديثات المستقبلية

### ميزات مقترحة:
- **تشفير إضافي** للبيانات الحساسة
- **انتهاء صلاحية** للبيانات المحفوظة
- **حفظ إعدادات متقدمة** (اللغة، الثيم، إلخ)
- **نسخ احتياطي** للإعدادات في السحابة

### تحسينات الأمان:
- **مصادقة بيومترية** قبل استرجاع البيانات
- **تشفير AES** للبيانات الحساسة
- **مهلة زمنية** لانتهاء صلاحية البيانات

## استكشاف الأخطاء

### مشاكل شائعة:

1. **البيانات لا تُحفظ**:
   - تأكد من تفعيل خيار "تذكرني"
   - تحقق من صلاحيات التطبيق

2. **البيانات لا تُحمّل**:
   - تأكد من استدعاء `_loadSavedCredentials()` في `initState()`
   - تحقق من وجود البيانات باستخدام `hasRememberedCredentials()`

3. **خطأ في المسح**:
   - تأكد من استدعاء `clearLoginCredentials()` بشكل صحيح
   - تحقق من رسائل الخطأ في الكونسول

## الخلاصة

هذه الميزة تحسن تجربة المستخدم بشكل كبير وتوفر مرونة في إدارة بيانات تسجيل الدخول مع الحفاظ على الأمان والخصوصية.
