# تطبيق موظفي Odoo

تطبيق Flutter يتصل بـ Odoo 15 عبر JSON-RPC API لعرض بيانات الموظفين.

## الميزات

- 🔐 تسجيل دخول آمن باستخدام Odoo API
- 💾 **حفظ بيانات تسجيل الدخول** - ميزة "تذكرني" لحفظ البيانات محلياً
- 👆 **تسجيل الدخول بالبصمة** - مصادقة بيومترية سريعة وآمنة
- 👤 عرض بيانات الموظف الحالي مع:
  - اسم الموظف
  - الرقم الوظيفي
  - الرقم الوطني
  - نوع الارتباط بالمؤسسة
- 👨‍💼 عرض معلومات المدير المباشر
- 📋 عرض أنواع الإجازات المتاحة في النظام
- ✉️ تقديم طلبات إجازة جديدة مع التقديم التلقائي للموافقة
- 🏖️ **حساب ذكي للإجازات** - استثناء الإجازات العامة للشركة من حساب أيام الإجازة المطلوبة
- 📊 عرض طلبات الإجازة السابقة مع حالة كل طلب
- 🔄 إعادة تحميل البيانات
- 📱 واجهة مستخدم عربية حديثة وأنيقة مع تابات
- 🎨 تصميم متجاوب يستغل كامل الشاشة
- 🌈 تدرجات لونية جميلة وتأثيرات بصرية
- 🔒 إعدادات خادم مخفية للأمان
- ✨ تأثيرات حركية وظلال متقدمة
- 🔑 **إدارة ذكية لبيانات تسجيل الدخول** مع خيارات الحفظ والمسح
- 🔐 **مصادقة بيومترية متقدمة** - دعم البصمة والوجه والأنماط
- 🛡️ **إعدادات آمنة** - حماية معلومات الخادم من التعرض في الكود
- 🔒 **تشفير متقدم** - حماية كلمات المرور بتشفير AES-256

## متطلبات النظام

- Flutter SDK (الإصدار 3.8.1 أو أحدث)
- Dart SDK
- خادم Odoo 15 يعمل ومتاح للوصول
- حساب مستخدم في Odoo مع صلاحيات الوصول لموديل `hr.employee`

## التثبيت والإعداد

### 1. تثبيت التبعيات
```bash
flutter pub get
```

### 2. إعداد خادم Odoo والإعدادات الآمنة

#### **الطريقة الجديدة الآمنة (مُوصى بها):**

**أ) إنشاء ملف .env:**
```bash
# انسخ ملف المثال
cp .env.example .env

# عدل الإعدادات في .env
ODOO_SERVER_URL=http://your-server:8069
ODOO_DATABASE=your_database_name
APP_ENVIRONMENT=development
```

**ب) أو استخدام متغيرات البيئة:**
```bash
# Windows
set ODOO_SERVER_URL=http://localhost:8069
set ODOO_DATABASE=my_database

# Linux/Mac
export ODOO_SERVER_URL=http://localhost:8069
export ODOO_DATABASE=my_database
```

#### **متطلبات الخادم:**
- خادم Odoo 15 يعمل ومتاح للوصول
- حساب مستخدم صالح مع صلاحيات الوصول لموديل `hr.employee`

### 3. تشغيل التطبيق
```bash
flutter run
```

## كيفية الاستخدام

### 1. تسجيل الدخول
- أدخل رابط خادم Odoo (مثل: `http://localhost:8069`)
- أدخل اسم قاعدة البيانات
- أدخل البريد الإلكتروني وكلمة المرور
- اضغط على "تسجيل الدخول"

### 2. عرض البيانات
بعد تسجيل الدخول بنجاح، ستظهر شاشة تحتوي على:
- اسم الموظف
- اسم المدير المباشر (إن وجد)
- معرف الموظف ومعرف المدير

### 3. طلب الإجازات مع الحساب الذكي
عند تقديم طلب إجازة جديد:
- **حساب تلقائي للأيام**: يتم حساب أيام العمل الفعلية فقط
- **استثناء الإجازات العامة**: الإجازات العامة للشركة لا تُحتسب من رصيدك
- **عرض الإجازات المستثناة**: يظهر التطبيق قائمة بالإجازات العامة التي تم استثناؤها
- **حساب دقيق**: يأخذ في الاعتبار جدول العمل الخاص بك (أيام العمل/الإجازات الأسبوعية)

### 4. ميزة حفظ بيانات تسجيل الدخول
- **تفعيل "تذكرني"**: في شاشة تسجيل الدخول، فعّل خيار "تذكر بيانات تسجيل الدخول"
- **الحفظ الآمن**: يتم حفظ البيانات بشكل مشفر على الجهاز
- **تسجيل دخول سريع**: في المرات القادمة ستجد البيانات محفوظة تلقائياً
- **إدارة البيانات**: يمكنك مسح البيانات المحفوظة عند تسجيل الخروج

### 5. ميزة تسجيل الدخول بالبصمة
- **تفعيل تلقائي**: عند تفعيل "تذكرني" سيُعرض عليك تفعيل البصمة
- **مصادقة سريعة**: تسجيل دخول فوري باستخدام بصمة الإصبع أو الوجه
- **أمان متقدم**: حماية إضافية للبيانات المحفوظة
- **دعم شامل**: يعمل مع جميع أنواع المصادقة البيومترية المتاحة
- **سهولة الاستخدام**: زر منفصل لتسجيل الدخول بالبصمة

### 6. الوظائف الإضافية
- **إعادة التحميل**: اضغط على أيقونة التحديث في شريط التطبيق
- **تسجيل الخروج**: اضغط على أيقونة تسجيل الخروج واختر بين الاحتفاظ بالبيانات أو مسحها

## بنية المشروع

```
lib/
├── main.dart                 # نقطة دخول التطبيق
├── models/
│   └── employee.dart         # نموذج بيانات الموظف
├── screens/
│   ├── login_screen.dart     # شاشة تسجيل الدخول
│   └── employee_screen.dart  # شاشة عرض بيانات الموظف
└── services/
    ├── odoo_service.dart     # خدمة الاتصال بـ Odoo API
    ├── storage_service.dart  # خدمة إدارة التخزين المحلي
    └── biometric_service.dart # خدمة المصادقة البيومترية
```

## API المستخدمة

### 1. المصادقة (Authentication)
```json
{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "service": "common",
    "method": "authenticate",
    "args": ["database", "email", "password", {}]
  },
  "id": 1
}
```

### 2. جلب بيانات الموظف
```json
{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "service": "object",
    "method": "execute_kw",
    "args": ["database", uid, "password", "hr.employee", "search_read",
             [["user_id", "=", uid]], {"fields": ["name", "parent_id"], "limit": 1}]
  },
  "id": 1
}
```

### 3. جلب الإجازات العامة للشركة
```json
{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "service": "object",
    "method": "execute_kw",
    "args": ["database", uid, "password", "resource.calendar.leaves", "search_read",
             [["resource_id", "=", false], ["date_from", "<=", "2024-12-31"], ["date_to", ">=", "2024-01-01"]],
             {"fields": ["name", "date_from", "date_to", "calendar_id"]}]
  },
  "id": 1
}
```

## كيفية عمل حساب الإجازات الذكي

### آلية الحساب:
1. **جلب جدول العمل**: يتم جلب جدول العمل الخاص بالموظف من `resource.calendar`
2. **تحديد أيام العمل**: استخراج أيام العمل الأسبوعية (مثل: الأحد-الخميس)
3. **جلب الإجازات العامة**: البحث في `resource.calendar.leaves` عن الإجازات التي:
   - `resource_id = false` (إجازة عامة للشركة وليس لموظف محدد)
   - `calendar_id` يطابق جدول عمل الموظف (أو null للإجازات العامة لجميع الجداول)
   - التواريخ تتداخل مع فترة الإجازة المطلوبة
4. **حساب الأيام الفعلية**:
   - تكرار عبر كل يوم في الفترة المطلوبة
   - التحقق من أنه يوم عمل حسب جدول العمل
   - التحقق من أنه ليس إجازة عامة
   - عد الأيام المؤهلة فقط

### مثال عملي:
```
طلب إجازة من 1 يناير إلى 10 يناير (10 أيام)
جدول العمل: الأحد-الخميس (5 أيام أسبوعياً)
إجازة عامة: 3 يناير (رأس السنة الميلادية)

النتيجة:
- إجمالي الأيام: 10
- أيام نهاية الأسبوع (الجمعة والسبت): 4 أيام
- الإجازات العامة: 1 يوم
- أيام العمل الفعلية المحتسبة: 5 أيام فقط
```

## ميزة حفظ بيانات تسجيل الدخول

### الأمان والخصوصية:
- **التشفير المحلي**: يتم حفظ البيانات باستخدام مكتبة `shared_preferences` الآمنة
- **التحكم الكامل**: المستخدم يختار متى يحفظ ومتى يمسح البيانات
- **الشفافية**: رسائل واضحة تشرح ما يحدث للبيانات

### كيفية الاستخدام:
1. **في شاشة تسجيل الدخول**:
   - أدخل بيانات تسجيل الدخول
   - فعّل خيار "تذكر بيانات تسجيل الدخول"
   - اضغط على أيقونة المعلومات لقراءة تفاصيل الأمان

2. **عند تسجيل الخروج**:
   - اختر "الخروج مع الاحتفاظ بالبيانات" للحفظ
   - أو اختر "مسح البيانات والخروج" لحذف البيانات المحفوظة

3. **في المرات القادمة**:
   - ستجد البيانات محفوظة تلقائياً
   - يمكنك تعديل خيار "تذكرني" في أي وقت

### الوظائف المتاحة:
- `StorageService.saveLoginCredentials()` - حفظ بيانات تسجيل الدخول
- `StorageService.getLoginCredentials()` - استرجاع البيانات المحفوظة
- `StorageService.clearLoginCredentials()` - مسح البيانات المحفوظة
- `StorageService.hasRememberedCredentials()` - التحقق من وجود بيانات محفوظة

## ميزة المصادقة البيومترية (البصمة)

### الأمان والتقنية:
- **مكتبة متقدمة**: استخدام `local_auth` للمصادقة البيومترية
- **دعم شامل**: البصمة، التعرف على الوجه، النمط، الرقم السري
- **تشفير محلي**: البيانات محمية بتشفير نظام التشغيل
- **عدم التخزين**: لا يتم حفظ البيانات البيومترية في التطبيق

### كيفية الاستخدام:
1. **التفعيل الأولي**:
   - سجل الدخول بالطريقة العادية مع تفعيل "تذكرني"
   - ستظهر نافذة لتفعيل البصمة تلقائياً
   - اختر "تفعيل" واتبع التعليمات

2. **تسجيل الدخول بالبصمة**:
   - في الشاشة الرئيسية ستجد زر "تسجيل الدخول بالبصمة"
   - اضغط على الزر واستخدم بصمتك
   - سيتم تسجيل الدخول فوراً

3. **إدارة الإعدادات**:
   - يمكن إلغاء تفعيل البصمة من إعدادات التطبيق
   - عند مسح بيانات تسجيل الدخول، يتم إلغاء البصمة تلقائياً

### الوظائف المتاحة:
- `BiometricService.checkBiometricStatus()` - فحص حالة البصمة
- `BiometricService.authenticate()` - تنفيذ المصادقة البيومترية
- `BiometricService.loginWithBiometric()` - تسجيل الدخول بالبصمة
- `BiometricService.enableBiometricAuth()` - تفعيل البصمة
- `BiometricService.disableBiometricAuth()` - إلغاء تفعيل البصمة

### متطلبات النظام:
- **Android**: الإصدار 6.0 (API 23) أو أحدث
- **iOS**: الإصدار 8.0 أو أحدث مع Touch ID/Face ID
- **أجهزة مدعومة**: جهاز مع مستشعر بصمة أو كاميرا للوجه

### استكشاف الأخطاء:
- **"الجهاز لا يدعم البصمة"**: تأكد من وجود مستشعر بصمة
- **"لم يتم تسجيل بصمات"**: سجل بصمتك في إعدادات الجهاز
- **"البصمة مقفلة"**: انتظر أو استخدم كلمة المرور لإلغاء القفل

📖 **للمزيد من المساعدة**: راجع [دليل حل مشاكل البصمة](BIOMETRIC_TROUBLESHOOTING.md)

## 🛡️ الأمان والإعدادات الآمنة

### الميزات الأمنية المطبقة:
- **🔒 تشفير كلمات المرور**: جميع كلمات المرور محفوظة بتشفير AES-256
- **🛡️ Certificate Pinning**: حماية متقدمة من هجمات Man-in-the-Middle
- **🛡️ إخفاء معلومات الخادم**: لا توجد معلومات حساسة مكشوفة في الكود
- **⚙️ إعدادات آمنة**: نظام بسيط وآمن لإدارة إعدادات الخادم
- **🔐 مصادقة بيومترية**: حماية إضافية باستخدام البصمة والوجه

### إعداد الإعدادات الآمنة:

#### **الطريقة الأولى - ملف .env:**
```bash
# أنشئ ملف .env في مجلد المشروع
ODOO_SERVER_URL=http://your-server:8069
ODOO_DATABASE=your_database_name
ODOO_API_KEY=your_api_key_if_needed
```

#### **الطريقة الثانية - متغيرات البيئة:**
```bash
# Linux/Mac
export ODOO_SERVER_URL=http://your-server:8069
export ODOO_DATABASE=your_database_name

# Windows
set ODOO_SERVER_URL=http://your-server:8069
set ODOO_DATABASE=your_database_name
```

📖 **للمزيد من التفاصيل**: راجع [دليل الإعدادات الآمنة](SECURE_CONFIGURATION.md)

## الاختبارات

لتشغيل الاختبارات:
```bash
flutter test
```

## التطوير المستقبلي

يمكن تطوير التطبيق لإضافة المزيد من الميزات:

- 📊 عرض المزيد من بيانات الموظف (القسم، المنصب، الراتب، إلخ)
- 📅 عرض الحضور والانصراف
- 📋 عرض المهام والمشاريع
- 📱 إشعارات push
- 🌙 وضع ليلي
- 🌐 دعم لغات متعددة
- 💾 حفظ بيانات تسجيل الدخول محلياً

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال**
   - تأكد من أن خادم Odoo يعمل
   - تحقق من صحة رابط الخادم
   - تأكد من إمكانية الوصول للخادم من الجهاز

2. **فشل المصادقة**
   - تحقق من صحة البريد الإلكتروني وكلمة المرور
   - تأكد من وجود المستخدم في قاعدة البيانات المحددة

3. **لا توجد بيانات موظف**
   - تأكد من ربط المستخدم بسجل موظف في Odoo
   - تحقق من صلاحيات المستخدم للوصول لموديل `hr.employee`

## 🛡️ ميزة Certificate Pinning - الحماية المتقدمة

### نظرة عامة:
تم إضافة **Certificate Pinning** لحماية التطبيق من هجمات **Man-in-the-Middle (MITM)** وضمان الاتصال الآمن مع خادم Odoo.

### الميزات الأمنية:
- **🔒 منع هجمات MITM**: حماية من اعتراض وتلاعب الاتصالات
- **🔍 التحقق من الشهادات**: التأكد من صحة شهادة الخادم
- **🔐 SHA-256 Fingerprinting**: استخدام بصمات آمنة للشهادات
- **⏱️ مهلة زمنية قابلة للتخصيص**: تحكم في وقت التحقق

### الإعداد:
```bash
# في ملف .env
CERTIFICATE_PINNING_ENABLED=true
CERTIFICATE_PINNING_SHA256=YOUR_CERTIFICATE_SHA256_FINGERPRINT
CERTIFICATE_PINNING_TIMEOUT=10
```

### للحصول على SHA-256 Fingerprint:
```bash
# باستخدام OpenSSL
openssl s_client -servername your-server.com -connect your-server.com:443 < /dev/null 2>/dev/null | openssl x509 -fingerprint -sha256 -noout -in /dev/stdin
```

📖 **للمزيد من التفاصيل**: راجع [دليل Certificate Pinning](CERTIFICATE_PINNING_FEATURE.md)
