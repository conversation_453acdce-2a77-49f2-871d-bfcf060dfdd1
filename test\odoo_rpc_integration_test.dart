import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/services/odoo_service.dart';

void main() {
  group('Odoo RPC Integration Tests', () {
    late OdooService odooService;

    setUp(() {
      odooService = OdooService(
        baseUrl: 'http://test-server:8069',
        database: 'test_db',
      );
    });

    test('should initialize OdooService with correct parameters', () {
      expect(odooService.baseUrl, equals('http://test-server:8069'));
      expect(odooService.database, equals('test_db'));
    });

    test('should handle authentication gracefully when server unavailable', () async {
      final result = await odooService.authenticate('<EMAIL>', 'password');
      expect(result, isNull);
    });

    test('should handle executeKw gracefully without authentication', () async {
      final result = await odooService.executeKw(
        uid: 1,
        password: 'test',
        model: 'res.partner',
        method: 'search_read',
        args: [],
        kwargs: {},
      );
      expect(result, isNull);
    });

    test('should handle getCurrentEmployee gracefully', () async {
      final result = await odooService.getCurrentEmployee(
        uid: 1,
        password: 'test',
      );
      expect(result, isNull);
    });

    test('should handle getLeaveTypes gracefully', () async {
      final result = await odooService.getLeaveTypes(
        uid: 1,
        password: 'test',
      );
      expect(result, isNull);
    });

    test('should handle createLeaveRequest gracefully', () async {
      final result = await odooService.createLeaveRequest(
        uid: 1,
        password: 'test',
        leaveRequestData: {
          'holiday_status_id': 1,
          'request_date_from': '2024-01-01',
          'request_date_to': '2024-01-02',
        },
      );
      expect(result, isNull);
    });

    test('should handle approveLeaveRequest gracefully', () async {
      final result = await odooService.approveLeaveRequest(
        uid: 1,
        password: 'test',
        leaveId: 1,
      );
      expect(result, isFalse);
    });

    test('should handle rejectLeaveRequest gracefully', () async {
      final result = await odooService.rejectLeaveRequest(
        uid: 1,
        password: 'test',
        leaveId: 1,
      );
      expect(result, isFalse);
    });

    test('should handle isLeaveManager gracefully', () async {
      final result = await odooService.isLeaveManager(
        uid: 1,
        password: 'test',
      );
      expect(result, isFalse);
    });

    test('should handle calculateWorkingDays gracefully', () async {
      final result = await odooService.calculateWorkingDays(
        uid: 1,
        password: 'test',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 5),
      );
      // Should return basic calculation when server unavailable
      expect(result, equals(5.0));
    });
  });
}
