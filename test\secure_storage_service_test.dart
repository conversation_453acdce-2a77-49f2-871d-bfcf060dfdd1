import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:odoo_employee_app/services/secure_storage_service.dart';

void main() {
  group('SecureStorageService Tests', () {
    setUp(() async {
      // تنظيف البيانات قبل كل اختبار
      SharedPreferences.setMockInitialValues({});
    });

    test('should initialize encryption service', () async {
      // تنفيذ
      await SecureStorageService.initialize();

      // تحقق - لا يجب أن يحدث خطأ
      expect(true, isTrue);
    });

    test('should encrypt and decrypt text correctly', () async {
      // ترتيب
      await SecureStorageService.initialize();
      const originalText = 'هذا نص سري للاختبار - Secret Test Text 123!@#';

      // تنفيذ
      final encryptedText = await SecureStorageService.encrypt(originalText);
      final decryptedText = await SecureStorageService.decrypt(encryptedText);

      // تحقق
      expect(decryptedText, equals(originalText));
      expect(
        encryptedText,
        isNot(equals(originalText)),
      ); // النص المشفر مختلف عن الأصلي
      expect(encryptedText.isNotEmpty, isTrue);
    });

    test('should handle different text types', () async {
      // ترتيب
      await SecureStorageService.initialize();

      final testCases = [
        'simple text',
        'نص عربي',
        '123456789',
        'Special@#\$%^&*()Characters',
        'Mixed نص مختلط 123 !@#',
        '', // نص فارغ
      ];

      // تنفيذ واختبار كل حالة
      for (final testText in testCases) {
        final encrypted = await SecureStorageService.encrypt(testText);
        final decrypted = await SecureStorageService.decrypt(encrypted);

        // تحقق
        expect(
          decrypted,
          equals(testText),
          reason: 'Failed for text: "$testText"',
        );
      }
    });

    test('should save and retrieve secure strings', () async {
      // ترتيب
      await SecureStorageService.initialize();
      const key = 'test_key';
      const value = 'secret_value_123';

      // تنفيذ
      await SecureStorageService.setSecureString(key, value);
      final retrievedValue = await SecureStorageService.getSecureString(key);

      // تحقق
      expect(retrievedValue, equals(value));
    });

    test('should return null for non-existent keys', () async {
      // ترتيب
      await SecureStorageService.initialize();
      const nonExistentKey = 'non_existent_key';

      // تنفيذ
      final value = await SecureStorageService.getSecureString(nonExistentKey);

      // تحقق
      expect(value, isNull);
    });

    test('should check if secure string exists', () async {
      // ترتيب
      await SecureStorageService.initialize();
      const key = 'test_key';
      const value = 'test_value';

      // تحقق عدم الوجود أولاً
      bool exists = await SecureStorageService.hasSecureString(key);
      expect(exists, isFalse);

      // حفظ القيمة
      await SecureStorageService.setSecureString(key, value);

      // تحقق الوجود
      exists = await SecureStorageService.hasSecureString(key);
      expect(exists, isTrue);
    });

    test('should remove secure strings', () async {
      // ترتيب
      await SecureStorageService.initialize();
      const key = 'test_key';
      const value = 'test_value';

      // حفظ القيمة
      await SecureStorageService.setSecureString(key, value);

      // تأكيد الوجود
      bool exists = await SecureStorageService.hasSecureString(key);
      expect(exists, isTrue);

      // تنفيذ - حذف القيمة
      await SecureStorageService.removeSecureString(key);

      // تحقق - عدم الوجود
      exists = await SecureStorageService.hasSecureString(key);
      expect(exists, isFalse);
    });

    test('should clear all encryption keys', () async {
      // ترتيب
      await SecureStorageService.initialize();
      await SecureStorageService.setSecureString('test_key', 'test_value');

      // تأكيد وجود البيانات
      bool hasData = await SecureStorageService.hasSecureString('test_key');
      expect(hasData, isTrue);

      // تنفيذ - مسح جميع مفاتيح التشفير
      await SecureStorageService.clearAllEncryptionKeys();

      // تحقق - عدم وجود البيانات بعد إعادة التهيئة
      await SecureStorageService.initialize();
      hasData = await SecureStorageService.hasSecureString('test_key');
      expect(hasData, isFalse);
    });

    test('should handle encryption errors gracefully', () async {
      // اختبار فك تشفير نص غير صحيح
      await SecureStorageService.initialize();

      try {
        await SecureStorageService.decrypt('invalid_encrypted_text');
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<Exception>());
        expect(e.toString(), contains('فشل في فك تشفير البيانات'));
      }
    });

    test('should generate unique encryption keys', () async {
      // تنفيذ - تهيئة متعددة
      await SecureStorageService.initialize();
      final key1 = 'test_key_1';
      await SecureStorageService.setSecureString(key1, 'test_value');

      await SecureStorageService.clearAllEncryptionKeys();
      await SecureStorageService.initialize();

      // تحقق - المفتاح القديم لا يعمل بعد إعادة التهيئة
      final value = await SecureStorageService.getSecureString(key1);
      expect(value, isNull);
    });

    test('should handle multiple concurrent operations', () async {
      // ترتيب
      await SecureStorageService.initialize();

      // تنفيذ - عمليات متزامنة متعددة
      final futures = <Future>[];
      for (int i = 0; i < 10; i++) {
        futures.add(SecureStorageService.setSecureString('key_$i', 'value_$i'));
      }

      await Future.wait(futures);

      // تحقق - جميع القيم محفوظة بشكل صحيح
      for (int i = 0; i < 10; i++) {
        final value = await SecureStorageService.getSecureString('key_$i');
        expect(value, equals('value_$i'));
      }
    });
  });
}
