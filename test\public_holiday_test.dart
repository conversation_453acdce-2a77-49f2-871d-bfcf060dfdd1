import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/models/public_holiday.dart';

void main() {
  group('PublicHoliday Model Tests', () {
    test('should create PublicHoliday from Odoo data correctly', () {
      // Arrange
      final odooData = {
        'id': 1,
        'name': 'رأس السنة الميلادية',
        'date_from': '2024-01-01 00:00:00',
        'date_to': '2024-01-01 23:59:59',
        'calendar_id': [1, 'جدول العمل الأساسي'],
      };

      // Act
      final holiday = PublicHoliday.fromOdooData(odooData);

      // Assert
      expect(holiday.id, equals(1));
      expect(holiday.name, equals('رأس السنة الميلادية'));
      expect(holiday.dateFrom.year, equals(2024));
      expect(holiday.dateFrom.month, equals(1));
      expect(holiday.dateFrom.day, equals(1));
      expect(holiday.calendarId, equals(1));
      expect(holiday.calendarName, equals('جدول العمل الأساسي'));
    });

    test('should handle missing calendar_id correctly', () {
      // Arrange
      final odooData = {
        'id': 2,
        'name': 'عيد الفطر',
        'date_from': '2024-04-10 00:00:00',
        'date_to': '2024-04-12 23:59:59',
        'calendar_id': null,
      };

      // Act
      final holiday = PublicHoliday.fromOdooData(odooData);

      // Assert
      expect(holiday.id, equals(2));
      expect(holiday.name, equals('عيد الفطر'));
      expect(holiday.calendarId, isNull);
      expect(holiday.calendarName, isNull);
    });

    test('should calculate number of days correctly for single day', () {
      // Arrange
      final holiday = PublicHoliday(
        id: 1,
        name: 'يوم واحد',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 1),
      );

      // Act & Assert
      expect(holiday.numberOfDays, equals(1));
    });

    test('should calculate number of days correctly for multiple days', () {
      // Arrange
      final holiday = PublicHoliday(
        id: 2,
        name: 'عطلة طويلة',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 5),
      );

      // Act & Assert
      expect(holiday.numberOfDays, equals(5));
    });

    test('should format date range correctly for single day', () {
      // Arrange
      final holiday = PublicHoliday(
        id: 1,
        name: 'يوم واحد',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 1),
      );

      // Act & Assert
      expect(holiday.formattedDateRange, equals('1/1/2024'));
    });

    test('should format date range correctly for multiple days', () {
      // Arrange
      final holiday = PublicHoliday(
        id: 2,
        name: 'عطلة طويلة',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 5),
      );

      // Act & Assert
      expect(holiday.formattedDateRange, equals('1/1/2024 - 5/1/2024'));
    });

    test('should check if date is contained in holiday period', () {
      // Arrange
      final holiday = PublicHoliday(
        id: 1,
        name: 'عطلة',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 5),
      );

      // Act & Assert
      expect(holiday.containsDate(DateTime(2024, 1, 1)), isTrue); // بداية الفترة
      expect(holiday.containsDate(DateTime(2024, 1, 3)), isTrue); // وسط الفترة
      expect(holiday.containsDate(DateTime(2024, 1, 5)), isTrue); // نهاية الفترة
      expect(holiday.containsDate(DateTime(2023, 12, 31)), isFalse); // قبل الفترة
      expect(holiday.containsDate(DateTime(2024, 1, 6)), isFalse); // بعد الفترة
    });

    test('should convert to map correctly', () {
      // Arrange
      final holiday = PublicHoliday(
        id: 1,
        name: 'رأس السنة',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 1),
        calendarId: 1,
        calendarName: 'جدول العمل',
      );

      // Act
      final map = holiday.toMap();

      // Assert
      expect(map['id'], equals(1));
      expect(map['name'], equals('رأس السنة'));
      expect(map['calendarId'], equals(1));
      expect(map['calendarName'], equals('جدول العمل'));
      expect(map['dateFrom'], isA<String>());
      expect(map['dateTo'], isA<String>());
    });

    test('should handle equality correctly', () {
      // Arrange
      final holiday1 = PublicHoliday(
        id: 1,
        name: 'عطلة',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 1),
      );

      final holiday2 = PublicHoliday(
        id: 1,
        name: 'عطلة',
        dateFrom: DateTime(2024, 1, 1),
        dateTo: DateTime(2024, 1, 1),
      );

      final holiday3 = PublicHoliday(
        id: 2,
        name: 'عطلة أخرى',
        dateFrom: DateTime(2024, 1, 2),
        dateTo: DateTime(2024, 1, 2),
      );

      // Act & Assert
      expect(holiday1, equals(holiday2));
      expect(holiday1, isNot(equals(holiday3)));
      expect(holiday1.hashCode, equals(holiday2.hashCode));
    });
  });
}
