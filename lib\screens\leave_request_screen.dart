import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../models/leave_type.dart';
import '../models/public_holiday.dart';
import '../config/app_config.dart';

/// شاشة طلب إجازة جديدة
class LeaveRequestScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;
  final LeaveType leaveType;

  const LeaveRequestScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
    required this.leaveType,
  });

  @override
  State<LeaveRequestScreen> createState() => _LeaveRequestScreenState();
}

class _LeaveRequestScreenState extends State<LeaveRequestScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  DateTime? _dateFrom;
  DateTime? _dateTo;
  double _numberOfDays = 0.0;
  bool _isSubmitting = false;
  List<PublicHoliday> _publicHolidays = [];
  double? _availableBalance;
  bool _isLoadingBalance = false;

  // متحكمات الحركة
  late AnimationController _cardController;
  late AnimationController _buttonController;
  late Animation<double> _cardAnimation;
  late Animation<double> _buttonAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadAvailableBalance();
  }

  @override
  void dispose() {
    _cardController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  /// تهيئة الحركات
  void _initAnimations() {
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _cardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _cardController, curve: Curves.easeOut));
    _buttonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.easeOut),
    );

    // بدء الحركات فوراً لتقليل التأخير
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cardController.forward();
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) _buttonController.forward();
        });
      }
    });
  }

  /// تحميل الرصيد المتاح لنوع الإجازة
  Future<void> _loadAvailableBalance() async {
    setState(() {
      _isLoadingBalance = true;
    });

    try {
      final balance = await widget.odooService.getEmployeeLeaveBalanceForType(
        uid: widget.uid,
        password: widget.password,
        leaveTypeId: widget.leaveType.id,
      );

      if (mounted) {
        setState(() {
          _availableBalance = balance;
          _isLoadingBalance = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _availableBalance = null;
          _isLoadingBalance = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // رأس الصفحة المتحرك
          _buildAnimatedHeader(),

          // محتوى الصفحة
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.spacing),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // بطاقة معلومات نوع الإجازة
                    AnimatedBuilder(
                      animation: _cardAnimation,
                      builder: (context, child) =>
                          Opacity(opacity: _cardAnimation.value, child: child!),
                      child: _buildLeaveTypeCard(),
                    ),

                    const SizedBox(height: AppConfig.largeSpacing),

                    // بطاقات التواريخ
                    SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.3),
                        end: Offset.zero,
                      ).animate(_cardAnimation),
                      child: FadeTransition(
                        opacity: _cardAnimation,
                        child: Column(
                          children: [
                            _buildDateCard(
                              title: 'تاريخ البداية',
                              date: _dateFrom,
                              icon: Icons.play_arrow,
                              color: const Color(0xFF10B981),
                              onTap: () => _selectDate(context, true),
                            ),
                            const SizedBox(height: AppConfig.spacing),
                            _buildDateCard(
                              title: 'تاريخ النهاية',
                              date: _dateTo,
                              icon: Icons.stop,
                              color: const Color(0xFFEF4444),
                              onTap: () => _selectDate(context, false),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: AppConfig.largeSpacing),

                    // بطاقة عدد الأيام
                    SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.5),
                        end: Offset.zero,
                      ).animate(_cardAnimation),
                      child: FadeTransition(
                        opacity: _cardAnimation,
                        child: _buildDaysCard(),
                      ),
                    ),

                    const SizedBox(height: AppConfig.largeSpacing),

                    // بطاقة الرصيد المتاح
                    SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.5),
                        end: Offset.zero,
                      ).animate(_cardAnimation),
                      child: FadeTransition(
                        opacity: _cardAnimation,
                        child: _buildBalanceCard(),
                      ),
                    ),

                    // عرض الإجازات العامة إذا كانت موجودة
                    if (_publicHolidays.isNotEmpty) ...[
                      const SizedBox(height: AppConfig.largeSpacing),
                      FadeTransition(
                        opacity: _cardAnimation,
                        child: _buildPublicHolidaysCard(),
                      ),
                    ],

                    const SizedBox(height: AppConfig.largeSpacing * 2),

                    // زر تقديم الطلب
                    ScaleTransition(
                      scale: _buttonAnimation,
                      child: _buildSubmitButton(),
                    ),

                    const SizedBox(height: AppConfig.spacing),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة المبسط
  Widget _buildAnimatedHeader() {
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF667EEA),
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConfig.spacing,
                vertical: AppConfig.smallSpacing,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppConfig.smallSpacing),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(
                        AppConfig.borderRadius,
                      ),
                    ),
                    child: const Icon(
                      Icons.event_note,
                      size: 28,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: AppConfig.smallSpacing),
                  Text(
                    'طلب إجازة جديدة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.leaveType.name,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات نوع الإجازة
  Widget _buildLeaveTypeCard() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFE0F7FA), Color(0xFFB2EBF2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        border: Border.all(color: const Color(0xFF7DD3FC), width: 2),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF7DD3FC).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConfig.spacing),
            decoration: BoxDecoration(
              color: const Color(0xFF7DD3FC),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            ),
            child: const Icon(
              Icons.event_available,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.leaveType.name,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppConfig.darkTextColor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة التاريخ
  Widget _buildDateCard({
    required String title,
    required DateTime? date,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.largeSpacing),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(width: AppConfig.spacing),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(AppConfig.darkTextColor),
                    ),
                  ),
                  const SizedBox(height: AppConfig.smallSpacing),
                  Text(
                    date != null
                        ? '${date.day}/${date.month}/${date.year}'
                        : 'اختر التاريخ',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: date != null ? color : Colors.grey.shade500,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة عدد الأيام
  Widget _buildDaysCard() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFF3E5F5), Color(0xFFE1BEE7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        border: Border.all(color: const Color(0xFFBA68C8), width: 2),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFBA68C8).withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConfig.spacing),
            decoration: BoxDecoration(
              color: const Color(0xFFBA68C8),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            ),
            child: const Icon(Icons.access_time, color: Colors.white, size: 24),
          ),
          const SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عدد الأيام',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppConfig.darkTextColor),
                  ),
                ),
                const SizedBox(height: AppConfig.smallSpacing),
                Text(
                  _numberOfDays > 0
                      ? '${_numberOfDays.toStringAsFixed(1)} يوم عمل'
                      : 'سيتم حسابه تلقائياً',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: const Color(0xFF6A1B9A),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (_numberOfDays > 0) ...[
                  const SizedBox(height: AppConfig.smallSpacing),
                  Text(
                    'تم استثناء الإجازات العامة وأيام نهاية الأسبوع',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(0xFF6A1B9A).withValues(alpha: 0.7),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الرصيد المتاح
  Widget _buildBalanceCard() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFE3F2FD), Color(0xFFBBDEFB)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2196F3).withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.account_balance_wallet,
              color: Color(0xFF1976D2),
              size: 28,
            ),
          ),
          const SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الرصيد المتاح',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: const Color(0xFF1976D2),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                if (_isLoadingBalance)
                  const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF1976D2),
                      ),
                    ),
                  )
                else if (_availableBalance != null)
                  Text(
                    '${_availableBalance!.toStringAsFixed(1)} يوم',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: const Color(0xFF1976D2),
                      fontWeight: FontWeight.w600,
                    ),
                  )
                else
                  Text(
                    'غير متاح',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _dateFrom = picked;
          // إذا كان تاريخ النهاية أقل من تاريخ البداية، قم بإعادة تعيينه
          if (_dateTo != null && _dateTo!.isBefore(_dateFrom!)) {
            _dateTo = null;
            _numberOfDays = 0.0;
          }
        } else {
          _dateTo = picked;
        }
        _calculateDays();
      });
    }
  }

  /// بناء بطاقة الإجازات العامة
  Widget _buildPublicHolidaysCard() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFE8F5E8), Color(0xFFC8E6C9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        border: Border.all(color: const Color(0xFF81C784), width: 2),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF81C784).withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConfig.spacing),
                decoration: BoxDecoration(
                  color: const Color(0xFF81C784),
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: const Icon(
                  Icons.celebration,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppConfig.spacing),
              Expanded(
                child: Text(
                  'إجازات عامة مُستثناة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2E7D32),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConfig.spacing),
          Text(
            'الأيام التالية هي إجازات عامة للشركة ولن تُحتسب من رصيد إجازاتك:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: const Color(0xFF2E7D32).withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: AppConfig.spacing),
          ...(_publicHolidays.map((holiday) {
            return Container(
              margin: const EdgeInsets.only(bottom: AppConfig.smallSpacing),
              padding: const EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                border: Border.all(
                  color: const Color(0xFF81C784).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.event, color: Color(0xFF2E7D32), size: 16),
                  const SizedBox(width: AppConfig.smallSpacing),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          holiday.name,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color: const Color(0xFF2E7D32),
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        Text(
                          holiday.formattedDateRange,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: const Color(
                                  0xFF2E7D32,
                                ).withValues(alpha: 0.7),
                              ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConfig.smallSpacing,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF81C784),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${holiday.numberOfDays} يوم',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            );
          })),
        ],
      ),
    );
  }

  /// بناء زر تقديم الطلب
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _canSubmit()
              ? [const Color(0xFF667EEA), const Color(0xFF764BA2)]
              : [Colors.grey.shade300, Colors.grey.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: _canSubmit()
            ? [
                BoxShadow(
                  color: const Color(0xFF667EEA).withValues(alpha: 0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ]
            : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _canSubmit() ? _submitRequest : null,
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          child: Center(
            child: _isSubmitting
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.send, color: Colors.white, size: 24),
                      const SizedBox(width: AppConfig.spacing),
                      Text(
                        'تقديم طلب الإجازة',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  /// حساب عدد الأيام (أيام العمل الفعلية) مع جلب الإجازات العامة
  void _calculateDays() async {
    if (_dateFrom != null && _dateTo != null) {
      // عرض مؤشر التحميل أثناء حساب أيام العمل
      setState(() {
        _numberOfDays = 0.0; // إعادة تعيين مؤقت
        _publicHolidays = []; // إعادة تعيين الإجازات العامة
      });

      try {
        // جلب الإجازات العامة للفترة المحددة
        final publicHolidays = await widget.odooService
            .getPublicHolidaysDetailed(
              uid: widget.uid,
              password: widget.password,
              dateFrom: _dateFrom!,
              dateTo: _dateTo!,
            );

        // حساب أيام العمل الفعلية باستخدام خدمة Odoo (التي تستثني الإجازات العامة الآن)
        final workingDays = await widget.odooService.calculateWorkingDays(
          uid: widget.uid,
          password: widget.password,
          dateFrom: _dateFrom!,
          dateTo: _dateTo!,
        );

        setState(() {
          _numberOfDays =
              workingDays ?? _dateTo!.difference(_dateFrom!).inDays + 1.0;
          _publicHolidays = publicHolidays ?? [];
        });
      } catch (e) {
        // في حالة الخطأ، استخدم الحساب التقليدي
        final difference = _dateTo!.difference(_dateFrom!).inDays + 1;
        setState(() {
          _numberOfDays = difference.toDouble();
          _publicHolidays = [];
        });
      }
    }
  }

  /// التحقق من إمكانية تقديم الطلب
  bool _canSubmit() {
    return _dateFrom != null &&
        _dateTo != null &&
        _numberOfDays > 0 &&
        !_isSubmitting;
  }

  /// تقديم طلب الإجازة
  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate() || !_canSubmit()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // التحقق من رصيد الإجازات أولاً
      final availableBalance = await widget.odooService
          .getEmployeeLeaveBalanceForType(
            uid: widget.uid,
            password: widget.password,
            leaveTypeId: widget.leaveType.id,
          );

      if (availableBalance == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'لا يمكن التحقق من رصيد الإجازات. يرجى المحاولة لاحقاً',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // التحقق من كفاية الرصيد
      if (availableBalance < _numberOfDays) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'لا يوجد لديك رصيد كافي من الإجازات\n'
                'الرصيد المتاح: ${availableBalance.toStringAsFixed(1)} يوم\n'
                'الأيام المطلوبة: ${_numberOfDays.toStringAsFixed(1)} يوم',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
        return;
      }

      // الحصول على معرف الموظف
      final employeeId = await widget.odooService.getEmployeeId(
        uid: widget.uid,
        password: widget.password,
      );

      if (employeeId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على بيانات الموظف'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // إعداد البيانات المطلوبة لـ Odoo
      final dateFromStr = _dateFrom!.toIso8601String().split('T')[0];
      final dateToStr = _dateTo!.toIso8601String().split('T')[0];

      final leaveRequestData = {
        'holiday_status_id': widget.leaveType.id,
        // التواريخ للعرض الداخلي
        'request_date_from': dateFromStr,
        'request_date_to': dateToStr,
        // التواريخ للعرض الخارجي (مع الوقت)
        'date_from': '$dateFromStr 08:00:00',
        'date_to': '$dateToStr 17:00:00',
        'number_of_days': _numberOfDays,
        'state': 'draft',
        'holiday_type': 'employee', // نوع الإجازة للموظف
        'employee_id': employeeId, // معرف الموظف الصحيح
      };

      final result = await widget.odooService.createLeaveRequest(
        uid: widget.uid,
        password: widget.password,
        leaveRequestData: leaveRequestData,
      );

      if (result != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء وتقديم طلب الإجازة للموافقة بنجاح!\nرقم الطلب: $result',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        Navigator.pop(context, true);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'فشل في تقديم طلب الإجازة\n'
              'تحقق من:\n'
              '• صحة التواريخ المحددة\n'
              '• توفر رصيد كافي من الإجازات\n'
              '• عدم تداخل مع إجازات أخرى',
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 6),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
