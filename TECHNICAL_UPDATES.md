# التحديثات التقنية - نظام الإجازات العامة

## نظرة عامة

تم إضافة ميزة جديدة لاستثناء الإجازات العامة للشركة من حساب أيام الإجازة المطلوبة من الموظف. هذا يضمن أن الموظفين لا يتم خصم أيام من رصيد إجازاتهم للأيام التي تكون فيها الشركة مغلقة رسمياً.

## الملفات المحدثة

### 1. نموذج البيانات الجديد
**الملف:** `lib/models/public_holiday.dart`
- نموذج جديد لتمثيل الإجازات العامة
- يحتوي على معلومات مفصلة عن كل إجازة عامة
- يوفر دوال مساعدة لحساب عدد الأيام وتنسيق التواريخ

### 2. تحديثات خدمة Odoo
**الملف:** `lib/services/odoo_service.dart`

#### الدوال الجديدة:
- `getPublicHolidays()`: جلب الإجازات العامة كقائمة تواريخ
- `getPublicHolidaysDetailed()`: جلب الإجازات العامة كنماذج بيانات مفصلة

#### الدوال المحدثة:
- `calculateWorkingDays()`: تم تحديثها لاستثناء الإجازات العامة من الحساب

### 3. تحديثات واجهة المستخدم
**الملف:** `lib/screens/leave_request_screen.dart`
- إضافة عرض للإجازات العامة المستثناة
- تحسين عرض عدد الأيام المحسوبة
- إضافة ملاحظات توضيحية للمستخدم

### 4. الاختبارات
**الملفات:**
- `test/public_holiday_test.dart`: اختبارات نموذج الإجازة العامة
- `test/odoo_service_public_holidays_test.dart`: اختبارات خدمة الإجازات العامة

## كيفية عمل النظام

### 1. جلب الإجازات العامة
```dart
// البحث في resource.calendar.leaves عن الإجازات التي:
List<dynamic> searchDomain = [
  ['resource_id', '=', false], // إجازة عامة (ليس لموظف محدد)
  ['date_from', '<=', dateTo.toIso8601String()],
  ['date_to', '>=', dateFrom.toIso8601String()],
];

// إضافة شرط جدول العمل إذا كان متوفراً
if (calendarId != null) {
  searchDomain.add(['calendar_id', '=', calendarId]);
}
```

### 2. حساب أيام العمل مع الاستثناءات
```dart
// لكل يوم في الفترة المطلوبة:
while (currentDate.isBefore(dateTo) || currentDate.isAtSameMomentAs(dateTo)) {
  // التحقق من أن اليوم ليس إجازة عامة
  if (!publicHolidayDates.contains(currentDateStr)) {
    // التحقق من أنه يوم عمل حسب جدول العمل
    if (workingDays.contains(odooWeekday)) {
      workingDaysCount += 1.0;
    }
  }
  currentDate = currentDate.add(const Duration(days: 1));
}
```

### 3. عرض المعلومات للمستخدم
- قائمة بالإجازات العامة المستثناة
- عدد الأيام لكل إجازة عامة
- اسم الإجازة العامة
- التواريخ المحددة

## مثال عملي

### السيناريو:
- طلب إجازة من 1 يناير إلى 10 يناير 2024
- جدول العمل: الأحد إلى الخميس
- إجازة عامة: 3 يناير (رأس السنة الميلادية)

### الحساب:
```
التواريخ: 1-10 يناير (10 أيام إجمالي)
أيام العمل حسب الجدول: 8 أيام (استثناء الجمعة والسبت)
الإجازات العامة: 1 يوم (3 يناير)
النتيجة النهائية: 7 أيام عمل فعلية
```

## الفوائد

### 1. للموظفين:
- عدالة في حساب الإجازات
- عدم خصم أيام للإجازات العامة
- شفافية في عرض الحسابات

### 2. للشركة:
- حسابات دقيقة للإجازات
- سهولة إدارة الإجازات العامة
- تقليل الأخطاء اليدوية

### 3. تقنياً:
- كود منظم وقابل للصيانة
- اختبارات شاملة
- توثيق واضح

## إعداد الإجازات العامة في Odoo

### 1. إنشاء إجازة عامة:
```python
# في Odoo shell أو من خلال الواجهة
public_holiday = self.env['resource.calendar.leaves'].create({
    'name': 'رأس السنة الميلادية',
    'date_from': '2024-01-01 00:00:00',
    'date_to': '2024-01-01 23:59:59',
    'resource_id': False,  # مهم: False يعني إجازة عامة
    'calendar_id': calendar_id,  # اختياري: لجدول عمل محدد
})
```

### 2. إجازة عامة لجميع الجداول:
```python
# عدم تحديد calendar_id يعني الإجازة تطبق على جميع جداول العمل
public_holiday = self.env['resource.calendar.leaves'].create({
    'name': 'العيد الوطني',
    'date_from': '2024-09-23 00:00:00',
    'date_to': '2024-09-23 23:59:59',
    'resource_id': False,
    'calendar_id': False,  # أو عدم تحديد هذا الحقل
})
```

## ملاحظات مهمة

### 1. الأداء:
- يتم جلب الإجازات العامة مرة واحدة لكل طلب
- استخدام Set للبحث السريع في التواريخ
- تحسين استعلامات قاعدة البيانات

### 2. الأمان:
- التحقق من صحة التواريخ
- معالجة الأخطاء بشكل مناسب
- عدم الاعتماد على البيانات الخارجية فقط

### 3. قابلية التوسع:
- إمكانية إضافة أنواع جديدة من الإجازات
- دعم جداول عمل متعددة
- مرونة في تخصيص الحسابات

## الخطوات التالية المقترحة

1. **إضافة إعدادات متقدمة**: السماح للمستخدمين بتخصيص طريقة الحساب
2. **تحسين الأداء**: إضافة تخزين مؤقت للإجازات العامة
3. **تقارير مفصلة**: إضافة تقارير تفصيلية عن استخدام الإجازات
4. **إشعارات**: تنبيه الموظفين بالإجازات العامة القادمة
5. **تكامل التقويم**: عرض الإجازات العامة في تقويم التطبيق
