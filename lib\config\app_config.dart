import '../services/environment_service.dart';

/// إعدادات التطبيق المصرفي الحديث
/// يستخدم نظام متغيرات البيئة الآمن لحماية المعلومات الحساسة
class AppConfig {
  // إعدادات Odoo الآمنة - تُقرأ من متغيرات البيئة أو ملفات الإعدادات
  // لا تحتوي على معلومات حساسة مكشوفة في الكود المصدري
  static String get defaultServerUrl => EnvironmentService.getServerUrl();
  static String get defaultDatabase => EnvironmentService.getDatabase();

  // إعدادات API
  static const int connectionTimeout = 30; // بالثواني
  static const int requestTimeout = 15; // بالثواني

  // إعدادات التطبيق
  static const String appName = 'بنك الموظفين';
  static const String appSubtitle = 'نظام إدارة الموظفين المصرفي';
  static const String appVersion = '1.0.0';

  // رسائل الخطأ
  static const String connectionErrorMessage = 'خطأ في الاتصال بالخادم';
  static const String authenticationErrorMessage = 'فشل في تسجيل الدخول';
  static const String noEmployeeDataMessage = 'لم يتم العثور على بيانات الموظف';
  static const String generalErrorMessage = 'حدث خطأ غير متوقع';

  // ألوان التطبيق المصرفي الحديث
  static const primaryColor = 0xFF3F91EB; // اللون الأساسي الأزرق
  static const whiteColor = 0xFFFFFFFF; // أبيض ناصع
  static const lightGrayColor = 0xFFF5F7FA; // رمادي فاتح للخلفيات
  static const darkTextColor = 0xFF1A1A1A; // أسود داكن للنصوص
  static const successColor = 0xFF28C76F; // أخضر هادئ للنجاح
  static const errorColor = 0xFFEA5455; // أحمر للتنبيهات
  static const cardShadowColor = 0x1A000000; // ظل خفيف للبطاقات
  static const dividerColor = 0xFFE8E8E8; // لون الفواصل
  static const secondaryTextColor = 0xFF6B7280; // رمادي للنصوص الثانوية

  // تدرجات لونية
  static const primaryGradient = [0xFF3F91EB, 0xFF2563EB]; // تدرج أزرق
  static const successGradient = [0xFF28C76F, 0xFF10B981]; // تدرج أخضر
  static const cardGradient = [0xFFFFFFFF, 0xFFF8FAFC]; // تدرج أبيض خفيف

  // أبعاد التصميم
  static const double borderRadius = 16.0; // زاوية مستديرة للبطاقات
  static const double largeBorderRadius = 24.0; // زاوية مستديرة كبيرة
  static const double cardElevation = 4.0; // ارتفاع البطاقات
  static const double buttonHeight = 56.0; // ارتفاع الأزرار
  static const double spacing = 16.0; // المسافات الأساسية
  static const double largeSpacing = 24.0; // المسافات الكبيرة
  static const double smallSpacing = 8.0; // المسافات الصغيرة

  // أحجام الخطوط
  static const double headlineFontSize = 28.0;
  static const double titleFontSize = 22.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;
  static const double smallFontSize = 12.0;

  // إعدادات Certificate Pinning
  static const bool enableCertificatePinning =
      true; // تفعيل Certificate Pinning
  static const int certificatePinningTimeout = 10; // مهلة التحقق بالثواني
  static const bool allowSelfSignedCertificates =
      false; // السماح بالشهادات الموقعة ذاتياً (للتطوير فقط)

  // إعدادات مهلة الجلسات
  static const bool enableSessionTimeout = true; // تفعيل مهلة الجلسات
  static const int defaultSessionTimeoutMinutes =
      2; // مهلة الجلسة الافتراضية بالدقائق
  static const int defaultSessionWarningMinutes =
      1; // تنبيه قبل انتهاء الجلسة بالدقائق
  static const bool enableSessionWarnings = true; // تفعيل تنبيهات انتهاء الجلسة
  static const bool enableAutoLogout = true; // تفعيل تسجيل الخروج التلقائي

  // وظائف إضافية للأمان

  /// الحصول على مفتاح API (إن وجد)
  static String get apiKey => EnvironmentService.getApiKey();

  /// التحقق من صحة الإعدادات
  static bool get isConfigurationValid =>
      EnvironmentService.validateConfiguration();

  /// تهيئة الإعدادات (يجب استدعاؤها عند بدء التطبيق)
  static Future<void> initialize() async {
    await EnvironmentService.initialize();
  }

  /// إعادة تحميل الإعدادات
  static Future<void> reload() async {
    await EnvironmentService.reload();
  }
}
