import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:odoo_employee_app/services/biometric_service.dart';
import 'package:odoo_employee_app/services/storage_service.dart';

void main() {
  group('BiometricService Tests', () {
    setUp(() async {
      // تنظيف البيانات قبل كل اختبار
      SharedPreferences.setMockInitialValues({});
    });

    test('should handle biometric status enum values', () {
      // اختبار قيم enum للحالات المختلفة
      expect(BiometricStatus.available, isA<BiometricStatus>());
      expect(BiometricStatus.notSupported, isA<BiometricStatus>());
      expect(BiometricStatus.notAvailable, isA<BiometricStatus>());
      expect(BiometricStatus.notEnrolled, isA<BiometricStatus>());
      expect(BiometricStatus.unknown, isA<BiometricStatus>());
    });

    test('should enable and disable biometric authentication', () async {
      // إلغاء تفعيل البصمة
      await BiometricService.disableBiometricAuth();
      bool isEnabled = await BiometricService.isBiometricEnabled();
      expect(isEnabled, isFalse);

      // محاكاة تفعيل البصمة
      await StorageService.setBiometricEnabled(true);
      isEnabled = await BiometricService.isBiometricEnabled();
      expect(isEnabled, isTrue);

      // إلغاء تفعيل البصمة
      await BiometricService.disableBiometricAuth();
      isEnabled = await BiometricService.isBiometricEnabled();
      expect(isEnabled, isFalse);
    });

    test('should handle biometric login result correctly', () {
      // نتيجة نجاح
      final successResult = BiometricLoginResult(
        success: true,
        email: '<EMAIL>',
        password: 'password123',
        serverUrl: 'http://localhost:8069',
        database: 'test_db',
      );

      expect(successResult.success, isTrue);
      expect(successResult.email, equals('<EMAIL>'));
      expect(successResult.password, equals('password123'));
      expect(successResult.errorMessage, isNull);

      // نتيجة فشل
      final failureResult = BiometricLoginResult(
        success: false,
        errorMessage: 'فشل في المصادقة',
        errorCode: 'AUTH_FAILED',
      );

      expect(failureResult.success, isFalse);
      expect(failureResult.email, isNull);
      expect(failureResult.errorMessage, equals('فشل في المصادقة'));
      expect(failureResult.errorCode, equals('AUTH_FAILED'));
    });

    test('should handle biometric auth result correctly', () {
      // نتيجة نجاح
      final successResult = BiometricAuthResult(success: true);

      expect(successResult.success, isTrue);
      expect(successResult.errorMessage, isNull);
      expect(successResult.errorCode, isNull);

      // نتيجة فشل
      final failureResult = BiometricAuthResult(
        success: false,
        errorMessage: 'فشل في التحقق من الهوية',
        errorCode: 'BIOMETRIC_ERROR',
      );

      expect(failureResult.success, isFalse);
      expect(failureResult.errorMessage, equals('فشل في التحقق من الهوية'));
      expect(failureResult.errorCode, equals('BIOMETRIC_ERROR'));
    });

    test(
      'should return failure when no credentials saved for biometric login',
      () async {
        // التأكد من عدم وجود بيانات محفوظة
        await StorageService.clearLoginCredentials();

        final result = await BiometricService.loginWithBiometric();

        expect(result.success, isFalse);
        expect(result.errorMessage, contains('البصمة غير مفعلة'));
      },
    );

    test('should save and retrieve biometric settings', () async {
      // حفظ إعداد البصمة
      await StorageService.setBiometricEnabled(true);
      bool isEnabled = await StorageService.isBiometricEnabled();
      expect(isEnabled, isTrue);

      // تغيير الإعداد
      await StorageService.setBiometricEnabled(false);
      isEnabled = await StorageService.isBiometricEnabled();
      expect(isEnabled, isFalse);

      // مسح الإعدادات
      await StorageService.clearBiometricSettings();
      isEnabled = await StorageService.isBiometricEnabled();
      expect(isEnabled, isFalse);
    });

    test('should handle biometric status enum correctly', () {
      // اختبار قيم enum
      expect(
        BiometricStatus.available.toString(),
        equals('BiometricStatus.available'),
      );
      expect(
        BiometricStatus.notSupported.toString(),
        equals('BiometricStatus.notSupported'),
      );
      expect(
        BiometricStatus.notAvailable.toString(),
        equals('BiometricStatus.notAvailable'),
      );
      expect(
        BiometricStatus.notEnrolled.toString(),
        equals('BiometricStatus.notEnrolled'),
      );
      expect(
        BiometricStatus.unknown.toString(),
        equals('BiometricStatus.unknown'),
      );
    });

    test('should create biometric login result with all parameters', () {
      final result = BiometricLoginResult(
        success: true,
        email: '<EMAIL>',
        password: 'secret123',
        serverUrl: 'https://server.com',
        database: 'production',
        errorMessage: null,
        errorCode: null,
      );

      expect(result.success, isTrue);
      expect(result.email, equals('<EMAIL>'));
      expect(result.password, equals('secret123'));
      expect(result.serverUrl, equals('https://server.com'));
      expect(result.database, equals('production'));
      expect(result.errorMessage, isNull);
      expect(result.errorCode, isNull);
    });

    test('should handle biometric enable result correctly', () {
      // نتيجة نجاح
      final successResult = BiometricEnableResult(
        success: true,
        message: 'تم تفعيل البصمة بنجاح',
      );

      expect(successResult.success, isTrue);
      expect(successResult.message, equals('تم تفعيل البصمة بنجاح'));
      expect(successResult.errorMessage, isNull);
      expect(successResult.statusCode, isNull);

      // نتيجة فشل
      final failureResult = BiometricEnableResult(
        success: false,
        errorMessage: 'الجهاز لا يدعم المصادقة البيومترية',
        statusCode: 'BiometricStatus.notSupported',
      );

      expect(failureResult.success, isFalse);
      expect(failureResult.message, isNull);
      expect(
        failureResult.errorMessage,
        equals('الجهاز لا يدعم المصادقة البيومترية'),
      );
      expect(failureResult.statusCode, equals('BiometricStatus.notSupported'));
    });
  });
}
