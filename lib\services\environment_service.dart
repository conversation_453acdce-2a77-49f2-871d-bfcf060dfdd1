import 'dart:io';
import 'package:flutter/services.dart';

/// خدمة إدارة الإعدادات الآمنة للإنتاج
/// تقرأ الإعدادات من متغيرات البيئة أو ملف .env
class EnvironmentService {
  // مفاتيح متغيرات البيئة
  static const String _keyServerUrl = 'ODOO_SERVER_URL';
  static const String _keyDatabase = 'ODOO_DATABASE';
  static const String _keyApiKey = 'ODOO_API_KEY';
  static const String _keyCertificatePinningEnabled =
      'CERTIFICATE_PINNING_ENABLED';
  static const String _keyCertificatePinningSha256 =
      'CERTIFICATE_PINNING_SHA256';
  static const String _keyCertificatePinningTimeout =
      'CERTIFICATE_PINNING_TIMEOUT';
  static const String _keySessionTimeoutEnabled = 'SESSION_TIMEOUT_ENABLED';
  static const String _keySessionTimeoutMinutes = 'SESSION_TIMEOUT_MINUTES';
  static const String _keySessionWarningMinutes = 'SESSION_WARNING_MINUTES';
  static const String _keyAutoLogoutEnabled = 'AUTO_LOGOUT_ENABLED';

  // القيم الافتراضية (يجب تغييرها في الإنتاج)
  // ملاحظة: هذه قيم وهمية للأمان - القيم الحقيقية يجب أن تكون في ملف .env
  static const String _defaultServerUrl = 'http://dummy-server.com';
  static const String _defaultDatabase = 'dummy-database';

  // إعدادات مخبأة
  static Map<String, String>? _cachedConfig;
  static bool _isInitialized = false;

  /// تهيئة خدمة البيئة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _cachedConfig = await _loadConfiguration();
      _isInitialized = true;
    } catch (e) {
      // استخدام الإعدادات الافتراضية في حالة الخطأ
      _cachedConfig = _getDefaultConfiguration();
      _isInitialized = true;
    }
  }

  /// تحميل الإعدادات من مصادر مختلفة
  static Future<Map<String, String>> _loadConfiguration() async {
    final config = <String, String>{};

    // 1. قراءة من متغيرات البيئة
    config.addAll(_loadFromEnvironmentVariables());

    // 2. قراءة من ملف الإعدادات المحلي (إن وجد)
    final localConfig = await _loadFromLocalConfigFile();
    config.addAll(localConfig);

    // 3. إضافة القيم الافتراضية للمفاتيح المفقودة
    _addDefaultValues(config);

    return config;
  }

  /// قراءة من متغيرات البيئة
  static Map<String, String> _loadFromEnvironmentVariables() {
    final config = <String, String>{};

    final serverUrl = Platform.environment[_keyServerUrl];
    final database = Platform.environment[_keyDatabase];
    final apiKey = Platform.environment[_keyApiKey];

    if (serverUrl != null) config['serverUrl'] = serverUrl;
    if (database != null) config['database'] = database;
    if (apiKey != null) config['apiKey'] = apiKey;

    return config;
  }

  /// قراءة من ملف الإعدادات المحلي
  static Future<Map<String, String>> _loadFromLocalConfigFile() async {
    final config = <String, String>{};

    try {
      // محاولة قراءة ملف .env من assets أولاً (للتطبيق المبني)
      try {
        final content = await rootBundle.loadString('.env');
        config.addAll(_parseEnvFile(content));
      } catch (assetError) {
        // إذا فشل قراءة من assets، جرب قراءة من نظام الملفات (للتطوير)
        final envFile = File('.env');
        if (await envFile.exists()) {
          final content = await envFile.readAsString();
          config.addAll(_parseEnvFile(content));
        }
      }
    } catch (e) {
      // تجاهل أخطاء قراءة ملف .env
    }

    return config;
  }

  /// تحليل محتوى ملف .env
  static Map<String, String> _parseEnvFile(String content) {
    final config = <String, String>{};
    final lines = content.split('\n');

    for (final line in lines) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty || trimmedLine.startsWith('#')) {
        continue; // تجاهل الأسطر الفارغة والتعليقات
      }

      final parts = trimmedLine.split('=');
      if (parts.length >= 2) {
        final key = parts[0].trim();
        final value = parts.sublist(1).join('=').trim();

        // إزالة علامات الاقتباس إن وجدت
        String cleanValue = value;
        if (cleanValue.startsWith('"') && cleanValue.endsWith('"')) {
          cleanValue = cleanValue.substring(1, cleanValue.length - 1);
        } else if (cleanValue.startsWith("'") && cleanValue.endsWith("'")) {
          cleanValue = cleanValue.substring(1, cleanValue.length - 1);
        }

        // تحويل مفاتيح البيئة إلى مفاتيح داخلية
        switch (key) {
          case _keyServerUrl:
            config['serverUrl'] = cleanValue;
            break;
          case _keyDatabase:
            config['database'] = cleanValue;
            break;
          case _keyApiKey:
            config['apiKey'] = cleanValue;
            break;
          case _keyCertificatePinningEnabled:
            config[_keyCertificatePinningEnabled] = cleanValue;
            break;
          case _keyCertificatePinningSha256:
            config[_keyCertificatePinningSha256] = cleanValue;
            break;
          case _keyCertificatePinningTimeout:
            config[_keyCertificatePinningTimeout] = cleanValue;
            break;
          case _keySessionTimeoutEnabled:
            config[_keySessionTimeoutEnabled] = cleanValue;
            break;
          case _keySessionTimeoutMinutes:
            config[_keySessionTimeoutMinutes] = cleanValue;
            break;
          case _keySessionWarningMinutes:
            config[_keySessionWarningMinutes] = cleanValue;
            break;
          case _keyAutoLogoutEnabled:
            config[_keyAutoLogoutEnabled] = cleanValue;
            break;
        }
      }
    }

    return config;
  }

  /// إضافة القيم الافتراضية للمفاتيح المفقودة
  static void _addDefaultValues(Map<String, String> config) {
    config.putIfAbsent('serverUrl', () => _defaultServerUrl);
    config.putIfAbsent('database', () => _defaultDatabase);
    // لا نضيف قيمة افتراضية لـ apiKey لأنه إجباري
  }

  /// الحصول على الإعدادات الافتراضية
  static Map<String, String> _getDefaultConfiguration() {
    return {
      'serverUrl': _defaultServerUrl,
      'database': _defaultDatabase,
      // لا نضيف apiKey لأنه إجباري ويجب أن يكون موجوداً في البيئة
    };
  }

  /// الحصول على رابط الخادم
  static String getServerUrl() {
    _ensureInitialized();
    return _cachedConfig!['serverUrl'] ?? _defaultServerUrl;
  }

  /// الحصول على اسم قاعدة البيانات
  static String getDatabase() {
    _ensureInitialized();
    return _cachedConfig!['database'] ?? _defaultDatabase;
  }

  /// الحصول على مفتاح API (مطلوب)
  static String getApiKey() {
    _ensureInitialized();
    return _cachedConfig!['apiKey'] ?? '';
  }

  /// التحقق من وجود مفتاح API
  static bool hasApiKey() {
    _ensureInitialized();
    final apiKey = _cachedConfig!['apiKey'] ?? '';
    return apiKey.isNotEmpty;
  }

  /// الحصول على جميع الإعدادات
  static Map<String, String> getAllSettings() {
    _ensureInitialized();
    return Map<String, String>.from(_cachedConfig!);
  }

  /// إعادة تحميل الإعدادات
  static Future<void> reload() async {
    _isInitialized = false;
    _cachedConfig = null;
    await initialize();
  }

  /// التحقق من صحة الإعدادات
  static bool validateConfiguration() {
    try {
      final serverUrl = getServerUrl();
      final database = getDatabase();
      final apiKey = getApiKey();

      // التحقق من أن القيم ليست وهمية
      if (serverUrl.contains('dummy-server') ||
          database.contains('dummy-database')) {
        return false;
      }

      // التحقق من صحة رابط الخادم
      final uri = Uri.tryParse(serverUrl);
      if (serverUrl.isEmpty || uri == null || !uri.hasScheme) {
        return false;
      }

      // التحقق من صحة اسم قاعدة البيانات
      if (database.isEmpty) {
        return false;
      }

      // التحقق من وجود مفتاح API (إجباري)
      if (apiKey.isEmpty) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من وجود قيم وهمية في الإعدادات
  static bool hasDummyValues() {
    try {
      final serverUrl = getServerUrl();
      final database = getDatabase();
      final apiKey = getApiKey();

      return serverUrl.contains('dummy-server') ||
          database.contains('dummy-database') ||
          apiKey.isEmpty;
    } catch (e) {
      return true; // في حالة الخطأ، اعتبر أن هناك قيم وهمية
    }
  }

  /// الحصول على رسالة تحذيرية إذا كانت الإعدادات تحتوي على قيم وهمية
  static String? getDummyValuesWarning() {
    if (hasDummyValues()) {
      final apiKey = getApiKey();
      if (apiKey.isEmpty) {
        return 'خطأ: مفتاح API (ODOO_API_KEY) مطلوب ولا يمكن أن يكون فارغاً. يرجى إضافته في ملف .env';
      }
      return 'تحذير: الإعدادات تحتوي على قيم وهمية. يرجى التأكد من وجود ملف .env مع القيم الصحيحة.';
    }
    return null;
  }

  /// الحصول على حالة تفعيل Certificate Pinning
  static bool getCertificatePinningEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCertificatePinningEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على SHA-256 fingerprint للشهادة المثبتة
  static String getCertificatePinningSha256() {
    _ensureInitialized();
    return _cachedConfig![_keyCertificatePinningSha256] ?? '';
  }

  /// الحصول على مهلة Certificate Pinning
  static int getCertificatePinningTimeout() {
    _ensureInitialized();
    final value = _cachedConfig![_keyCertificatePinningTimeout] ?? '10';
    return int.tryParse(value) ?? 10;
  }

  /// الحصول على حالة تفعيل مهلة الجلسات
  static bool getSessionTimeoutEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionTimeoutEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// الحصول على مدة مهلة الجلسة بالدقائق
  static int getSessionTimeoutMinutes() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionTimeoutMinutes] ?? '30';
    return int.tryParse(value) ?? 30;
  }

  /// الحصول على مدة التنبيه قبل انتهاء الجلسة بالدقائق
  static int getSessionWarningMinutes() {
    _ensureInitialized();
    final value = _cachedConfig![_keySessionWarningMinutes] ?? '5';
    return int.tryParse(value) ?? 5;
  }

  /// الحصول على حالة تفعيل تسجيل الخروج التلقائي
  static bool getAutoLogoutEnabled() {
    _ensureInitialized();
    final value = _cachedConfig![_keyAutoLogoutEnabled] ?? 'true';
    return value.toLowerCase() == 'true';
  }

  /// التأكد من تهيئة الخدمة
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception(
        'خدمة البيئة غير مهيأة. يرجى استدعاء EnvironmentService.initialize() أولاً',
      );
    }
  }
}
