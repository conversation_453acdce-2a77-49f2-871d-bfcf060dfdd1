import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/models/employee.dart';

void main() {
  group('Employee Image Handling Tests', () {
    test('should handle valid base64 image string', () {
      final data = {
        'id': 1,
        'name': 'أحمد محمد',
        'national_number': '123456789',
        'parent_id': [2, 'مدير الموارد البشرية'],
        'connected_with_comp': 'شركة الاختبار',
        'resource_calendar_id': [1, 'دوام عادي'],
        'image_1920':
            'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      };

      final employee = Employee.fromOdooData(data);

      expect(employee.image1920, isNotNull);
      expect(employee.image1920, isA<String>());
      expect(employee.image1920!.isNotEmpty, true);
    });

    test('should handle false value for image_1920', () {
      final data = {
        'id': 1,
        'name': 'أحمد محمد',
        'national_number': '123456789',
        'parent_id': false,
        'connected_with_comp': false,
        'resource_calendar_id': false,
        'image_1920': false, // هذا ما يسبب المشكلة
      };

      final employee = Employee.fromOdooData(data);

      expect(employee.image1920, isNull);
    });

    test('should handle null value for image_1920', () {
      final data = {
        'id': 1,
        'name': 'أحمد محمد',
        'national_number': '123456789',
        'parent_id': false,
        'connected_with_comp': false,
        'resource_calendar_id': false,
        'image_1920': null,
      };

      final employee = Employee.fromOdooData(data);

      expect(employee.image1920, isNull);
    });

    test('should handle empty string for image_1920', () {
      final data = {
        'id': 1,
        'name': 'أحمد محمد',
        'national_number': '123456789',
        'parent_id': false,
        'connected_with_comp': false,
        'resource_calendar_id': false,
        'image_1920': '',
      };

      final employee = Employee.fromOdooData(data);

      expect(employee.image1920, isNull);
    });

    test('should handle non-string value for image_1920', () {
      final data = {
        'id': 1,
        'name': 'أحمد محمد',
        'national_number': '123456789',
        'parent_id': false,
        'connected_with_comp': false,
        'resource_calendar_id': false,
        'image_1920': 123, // رقم بدلاً من نص
      };

      final employee = Employee.fromOdooData(data);

      expect(employee.image1920, isNull);
    });

    test('should handle missing image_1920 field', () {
      final data = {
        'id': 1,
        'name': 'أحمد محمد',
        'national_number': '123456789',
        'parent_id': false,
        'connected_with_comp': false,
        'resource_calendar_id': false,
        // لا يوجد حقل image_1920
      };

      final employee = Employee.fromOdooData(data);

      expect(employee.image1920, isNull);
    });

    test('should preserve valid image data in toMap', () {
      final employee = Employee(
        id: 1,
        name: 'أحمد محمد',
        nationalNumber: '123456789',
        connectedWithComp: 'شركة اختبار',
        managerName: null,
        managerId: null,
        resourceCalendarId: null,
        image1920: 'valid_base64_string',
      );

      final map = employee.toMap();

      expect(map['image_1920'], equals('valid_base64_string'));
    });

    test('should handle null image in toMap', () {
      final employee = Employee(
        id: 1,
        name: 'أحمد محمد',
        nationalNumber: '123456789',
        connectedWithComp: null,
        managerName: null,
        managerId: null,
        resourceCalendarId: null,
        image1920: null,
      );

      final map = employee.toMap();

      expect(map['image_1920'], isNull);
    });
  });
}
