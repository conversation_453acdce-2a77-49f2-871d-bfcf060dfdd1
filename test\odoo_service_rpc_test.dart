import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/services/odoo_service.dart';

void main() {
  group('OdooService with odoo_rpc Tests', () {
    late OdooService odooService;

    setUp(() {
      // إعداد خدمة Odoo للاختبار
      odooService = OdooService(
        baseUrl: 'http://localhost:8069',
        database: 'test_db',
      );
    });

    test('should create OdooService instance', () {
      expect(odooService, isNotNull);
      expect(odooService.baseUrl, equals('http://localhost:8069'));
      expect(odooService.database, equals('test_db'));
    });

    test('authenticate should return null for invalid credentials', () async {
      // هذا الاختبار سيفشل لأن الخادم غير متاح، لكنه يتحقق من أن الدالة تعمل
      final result = await odooService.authenticate('<EMAIL>', 'wrong_password');
      expect(result, isNull);
    });

    test('executeKw should handle invalid parameters gracefully', () async {
      // اختبار أن الدالة تتعامل مع المعاملات غير الصحيحة بشكل صحيح
      final result = await odooService.executeKw(
        uid: 1,
        password: 'test',
        model: 'res.partner',
        method: 'search_read',
        args: [],
        kwargs: {},
      );
      expect(result, isNull);
    });
  });
}
