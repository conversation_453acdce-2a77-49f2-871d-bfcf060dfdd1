# دليل حل مشاكل البصمة

## 🔍 المشاكل الشائعة وحلولها

### 1. رسالة "فشل في تفعيل البصمة"

#### الأسباب المحتملة:
- الجهاز لا يدعم البصمة
- لم يتم تسجيل بصمات على الجهاز
- البصمة مقفلة مؤقتاً
- مشكلة في إعدادات الأمان

#### الحلول:

##### أ) التحقق من دعم الجهاز:
```
1. افتح إعدادات الجهاز
2. ابح<PERSON> عن "الأمان" أو "Security"
3. تحقق من وجود خيار "البصمة" أو "Fingerprint"
4. إذا لم تجده، فالجهاز لا يدعم البصمة
```

##### ب) تسجيل بصمة جديدة:
```
1. اذهب إلى إعدادات الجهاز
2. اختر "الأمان والخصوصية"
3. اختر "البصمة"
4. اتبع التعليمات لتسجيل بصمتك
5. أعد تشغيل التطبيق وحاول مرة أخرى
```

##### ج) إلغاء قفل البصمة:
```
1. استخدم كلمة مرور الجهاز أو النمط
2. اذهب إلى إعدادات البصمة
3. أعد تفعيل البصمة
4. جرب التطبيق مرة أخرى
```

---

### 2. رسالة "الجهاز غير مدعوم"

#### المعنى:
هذا الجهاز لا يحتوي على مستشعر بصمة أو لا يدعم المصادقة البيومترية.

#### الحل:
- استخدم ميزة "تذكرني" العادية
- التطبيق سيعمل بشكل طبيعي بدون البصمة
- يمكنك الاستمتاع بجميع الميزات الأخرى

---

### 3. رسالة "لا توجد بصمات مسجلة"

#### المعنى:
الجهاز يدعم البصمة لكن لم يتم تسجيل أي بصمات عليه.

#### الحل خطوة بخطوة:

##### للأندرويد:
```
1. اذهب إلى الإعدادات (Settings)
2. اختر "الأمان" (Security)
3. اختر "البصمة" (Fingerprint)
4. اضغط "إضافة بصمة" (Add fingerprint)
5. اتبع التعليمات على الشاشة
6. ضع إصبعك على المستشعر عدة مرات
7. عند الانتهاء، أعد فتح التطبيق
```

##### للآيفون:
```
1. اذهب إلى الإعدادات (Settings)
2. اختر "Touch ID & Passcode" أو "Face ID & Passcode"
3. أدخل كلمة المرور
4. اضغط "Add a Fingerprint" أو فعّل Face ID
5. اتبع التعليمات
6. أعد فتح التطبيق
```

---

### 4. رسالة "البصمة مقفلة"

#### المعنى:
تم قفل البصمة بسبب عدة محاولات فاشلة.

#### الحل:
```
1. انتظر 30 ثانية على الأقل
2. استخدم كلمة مرور الجهاز لإلغاء القفل
3. أو أعد تشغيل الجهاز
4. جرب البصمة مرة أخرى
```

---

### 5. البصمة تعمل في التطبيقات الأخرى لكن ليس في تطبيقنا

#### الأسباب المحتملة:
- لم يتم تفعيل "تذكرني" أولاً
- مشكلة في صلاحيات التطبيق
- حاجة لإعادة تشغيل التطبيق

#### الحل:
```
1. تأكد من تفعيل "تذكر بيانات تسجيل الدخول" أولاً
2. سجل الدخول بنجاح مرة واحدة
3. عند ظهور نافذة تفعيل البصمة، اختر "تفعيل"
4. إذا لم تظهر النافذة، أعد تشغيل التطبيق
```

---

### 6. رسالة "local_auth plugin requires activity to be a FragmentActivity"

#### المعنى:
هذا خطأ تقني في التطبيق يحدث بسبب مشكلة في إعدادات Android.

#### الحل:
```
1. أغلق التطبيق تماماً (من قائمة التطبيقات الحديثة)
2. أعد فتح التطبيق
3. حاول تفعيل البصمة مرة أخرى
4. إذا استمرت المشكلة، أعد تشغيل الجهاز
```

#### إذا لم ينجح الحل:
- تأكد من أن التطبيق محدث لأحدث إصدار
- امسح cache التطبيق من إعدادات الجهاز
- أعد تثبيت التطبيق إذا لزم الأمر

---

### 7. زر البصمة لا يظهر

#### الأسباب:
- لم يتم تفعيل "تذكرني"
- البصمة غير مفعلة
- لا توجد بيانات محفوظة

#### الحل:
```
1. سجل الدخول مع تفعيل "تذكر بيانات تسجيل الدخول"
2. اختر "تفعيل" عند ظهور نافذة البصمة
3. أعد فتح التطبيق
4. يجب أن يظهر الزر الأخضر للبصمة
```

---

## 🛠️ خطوات التشخيص

### الخطوة 1: فحص أساسي
```
□ هل الجهاز يدعم البصمة؟
□ هل تم تسجيل بصمة على الجهاز؟
□ هل البصمة تعمل في التطبيقات الأخرى؟
□ هل تم تفعيل "تذكرني" في التطبيق؟
```

### الخطوة 2: اختبار البصمة
```
□ جرب البصمة في إعدادات الجهاز
□ جرب البصمة في تطبيق آخر (مثل البنك)
□ تأكد من نظافة مستشعر البصمة
□ جرب أصابع مختلفة
```

### الخطوة 3: إعادة التفعيل
```
□ امسح بيانات التطبيق المحفوظة
□ سجل الدخول من جديد مع "تذكرني"
□ فعّل البصمة عند ظهور النافذة
□ أعد تشغيل التطبيق
```

---

## 📱 حسب نوع الجهاز

### أجهزة Samsung:
- اذهب إلى الإعدادات > المقاييس الحيوية والأمان > البصمات
- تأكد من تفعيل "فتح الجهاز" و "التطبيقات"

### أجهزة Huawei:
- اذهب إلى الإعدادات > الأمان والخصوصية > البصمة
- فعّل "فتح الشاشة" و "الوصول إلى التطبيقات"

### أجهزة Xiaomi:
- اذهب إلى الإعدادات > كلمات المرور والأمان > البصمة
- تأكد من تفعيل جميع الخيارات

### أجهزة iPhone:
- اذهب إلى الإعدادات > Touch ID ورمز الدخول
- تأكد من تفعيل "فتح iPhone" و"التطبيقات"

---

## 🆘 إذا لم تنجح الحلول

### اتصل بالدعم الفني:
1. اذكر نوع جهازك وإصدار النظام
2. اذكر الرسالة الدقيقة التي تظهر
3. اذكر الخطوات التي جربتها

### معلومات مفيدة للدعم:
- نوع الجهاز: (Samsung, iPhone, إلخ)
- إصدار النظام: (Android 12, iOS 15, إلخ)
- إصدار التطبيق
- هل البصمة تعمل في تطبيقات أخرى؟

---

## ✅ نصائح للاستخدام الأمثل

### للحصول على أفضل تجربة:
1. **نظف المستشعر** بانتظام بقطعة قماش ناعمة
2. **سجل عدة بصمات** (إصبع السبابة والإبهام)
3. **أعد تسجيل البصمة** إذا تغيرت (جرح، جفاف، إلخ)
4. **استخدم كلمة مرور قوية** كبديل
5. **حدث التطبيق** للحصول على أحدث التحسينات

### أمان إضافي:
- لا تشارك بصمتك مع أحد
- استخدم قفل الشاشة دائماً
- فعّل التنبيهات الأمنية في الجهاز
- راجع التطبيقات المسموح لها باستخدام البصمة

---

## 🎯 الخلاصة

معظم مشاكل البصمة تحل بـ:
1. **التأكد من تسجيل البصمة** في إعدادات الجهاز
2. **تفعيل "تذكرني"** في التطبيق أولاً
3. **إعادة تشغيل التطبيق** بعد التفعيل
4. **تنظيف المستشعر** والمحاولة مرة أخرى

إذا استمرت المشكلة، يمكنك استخدام التطبيق بشكل طبيعي مع ميزة "تذكرني" العادية.
