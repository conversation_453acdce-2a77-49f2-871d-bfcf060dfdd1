// اختبارات تطبيق موظفي Odoo
//
// هذا ملف اختبار أساسي للتطبيق

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:odoo_employee_app/main.dart';
import 'package:odoo_employee_app/providers/language_provider.dart';

void main() {
  testWidgets('App should start with login screen', (
    WidgetTester tester,
  ) async {
    // بناء التطبيق وتشغيل إطار مع Provider
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (context) => LanguageProvider(),
        child: const BankEmployeeApp(),
      ),
    );

    // التحقق من وجود شاشة تسجيل الدخول
    expect(find.text('تسجيل الدخول'), findsAtLeastNWidgets(1));
    expect(find.text('بنك الموظفين'), findsOneWidget);

    // التحقق من وجود حقول الإدخال
    expect(find.byType(TextFormField), findsWidgets);

    // التحقق من وجود زر تسجيل الدخول
    expect(find.byType(ElevatedButton), findsOneWidget);
  });
}
