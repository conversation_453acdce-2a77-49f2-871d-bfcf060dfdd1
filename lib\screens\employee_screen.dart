import 'package:flutter/material.dart';
import 'dart:convert';
import '../services/odoo_service.dart';
import '../models/employee.dart';
import '../config/app_config.dart';
import 'login_screen.dart';
import 'leave_types_screen.dart';
import 'my_leave_requests_screen.dart';
import 'leave_approval_screen.dart';
import 'settings_screen.dart';

/// شاشة الموظف المصرفية الحديثة مع تصميم متطور
class EmployeeScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const EmployeeScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<EmployeeScreen> createState() => _EmployeeScreenState();
}

class _EmployeeScreenState extends State<EmployeeScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Employee? _employee;
  bool _isLoading = true;
  String? _errorMessage;
  int _currentIndex = 0;
  bool _isLeaveManager = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    // تحديد عدد التبويبات بناءً على ما إذا كان المستخدم مدير إجازات
    _tabController = TabController(length: 3, vsync: this);
    _loadEmployeeData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الموظف من Odoo
  Future<void> _loadEmployeeData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final employeeData = await widget.odooService.getCurrentEmployee(
        uid: widget.uid,
        password: widget.password,
      );

      // فحص ما إذا كان المستخدم مدير إجازات
      final isManager = await widget.odooService.isLeaveManager(
        uid: widget.uid,
        password: widget.password,
      );

      if (employeeData != null) {
        setState(() {
          _employee = Employee.fromOdooData(employeeData);
          _isLeaveManager = isManager;
          _isLoading = false;
        });

        // إعادة إنشاء TabController بالعدد الصحيح من التبويبات
        _tabController.dispose();
        _tabController = TabController(
          length: _isLeaveManager ? 4 : 3,
          vsync: this,
        );
      } else {
        setState(() {
          _errorMessage = 'لم يتم العثور على بيانات الموظف';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// تسجيل الخروج والعودة إلى شاشة تسجيل الدخول
  void _logout() {
    // تسجيل الخروج فوراً مع الاحتفاظ بالبيانات
    _logoutKeepData();
  }

  /// تسجيل الخروج مع الاحتفاظ بالبيانات
  void _logoutKeepData() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    await _loadEmployeeData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(AppConfig.lightGrayColor),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildModernAppBar(),
              Expanded(
                child: _isLoading
                    ? _buildLoadingState()
                    : _errorMessage != null
                    ? _buildErrorState()
                    : _buildMainContent(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  /// بناء شريط التطبيق الحديث
  Widget _buildModernAppBar() {
    return Container(
      padding: const EdgeInsets.only(
        top: AppConfig.largeSpacing * 2,
        left: AppConfig.spacing,
        right: AppConfig.spacing,
        bottom: AppConfig.spacing,
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(AppConfig.primaryColor), Color(0xFF2563EB)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppConfig.largeBorderRadius),
          bottomRight: Radius.circular(AppConfig.largeBorderRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.primaryColor).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // أيقونة الإعدادات
            GestureDetector(
              onTap: () => _navigateToSettings(),
              child: Container(
                padding: const EdgeInsets.all(AppConfig.smallSpacing),
                decoration: BoxDecoration(
                  color: const Color(
                    AppConfig.whiteColor,
                  ).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: const Icon(
                  Icons.settings,
                  color: Color(AppConfig.whiteColor),
                  size: 28,
                ),
              ),
            ),
            const SizedBox(width: AppConfig.spacing),

            // معلومات التطبيق
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppConfig.appName,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: const Color(AppConfig.whiteColor),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _employee?.name ?? 'مرحباً بك',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(
                        AppConfig.whiteColor,
                      ).withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            // أزرار الإجراءات
            Row(
              children: [
                _buildActionButton(
                  icon: Icons.refresh,
                  onPressed: _isLoading ? null : _refreshData,
                  tooltip: 'إعادة تحميل',
                ),
                const SizedBox(width: AppConfig.smallSpacing),
                _buildActionButton(
                  icon: Icons.logout,
                  onPressed: _logout,
                  tooltip: 'تسجيل الخروج',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء في شريط التطبيق
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(AppConfig.whiteColor).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
      ),
      child: IconButton(
        icon: Icon(icon, color: const Color(AppConfig.whiteColor), size: 22),
        onPressed: onPressed,
        tooltip: tooltip,
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(AppConfig.largeSpacing),
            decoration: BoxDecoration(
              color: const Color(AppConfig.whiteColor),
              borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
              boxShadow: [
                BoxShadow(
                  color: const Color(AppConfig.cardShadowColor),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                const CircularProgressIndicator(
                  color: Color(AppConfig.primaryColor),
                  strokeWidth: 3,
                ),
                const SizedBox(height: AppConfig.spacing),
                Text(
                  'جاري تحميل بيانات الموظف...',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: const Color(AppConfig.darkTextColor),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(AppConfig.largeSpacing),
        padding: const EdgeInsets.all(AppConfig.largeSpacing),
        decoration: BoxDecoration(
          color: const Color(AppConfig.whiteColor),
          borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
          boxShadow: [
            BoxShadow(
              color: const Color(AppConfig.cardShadowColor),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: const Color(AppConfig.errorColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: const Icon(
                Icons.error_outline,
                color: Color(AppConfig.errorColor),
                size: 48,
              ),
            ),
            const SizedBox(height: AppConfig.spacing),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: const Color(AppConfig.darkTextColor),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.smallSpacing),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: const Color(AppConfig.secondaryTextColor),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.largeSpacing),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent() {
    final children = [
      _buildEmployeeProfile(),
      LeaveTypesScreen(
        odooService: widget.odooService,
        uid: widget.uid,
        password: widget.password,
      ),
      MyLeaveRequestsScreen(
        odooService: widget.odooService,
        uid: widget.uid,
        password: widget.password,
      ),
    ];

    // إضافة شاشة الموافقة للمديرين فقط
    if (_isLeaveManager) {
      children.add(
        LeaveApprovalScreen(
          odooService: widget.odooService,
          uid: widget.uid,
          password: widget.password,
        ),
      );
    }

    return IndexedStack(index: _currentIndex, children: children);
  }

  /// بناء ملف الموظف الشخصي
  Widget _buildEmployeeProfile() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      color: const Color(AppConfig.primaryColor),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppConfig.spacing),
        child: Column(
          children: [
            _buildProfileCard(),
            const SizedBox(height: AppConfig.spacing),
            _buildQuickActions(),
            const SizedBox(height: AppConfig.spacing),
            _buildStatsCards(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الملف الشخصي
  Widget _buildProfileCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(AppConfig.whiteColor), Color(0xFFF8FAFC)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.cardShadowColor),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // صورة الموظف
          _buildEmployeeAvatar(),
          const SizedBox(height: AppConfig.spacing),

          // اسم الموظف
          Text(
            _employee?.name ?? 'غير محدد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppConfig.darkTextColor),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.smallSpacing),

          // المنصب أو نوع الارتباط
          if (_employee?.connectionTypeText != null)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConfig.spacing,
                vertical: AppConfig.smallSpacing,
              ),
              decoration: BoxDecoration(
                color: const Color(
                  AppConfig.primaryColor,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Text(
                _employee!.connectionTypeText!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: const Color(AppConfig.primaryColor),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

          const SizedBox(height: AppConfig.largeSpacing),

          // معلومات إضافية
          _buildInfoRows(),
        ],
      ),
    );
  }

  /// بناء صفوف المعلومات
  Widget _buildInfoRows() {
    return Column(
      children: [
        if (_employee?.nationalNumber != null)
          _buildInfoRow(
            icon: Icons.credit_card,
            label: 'الرقم الوطني',
            value: _employee!.nationalNumber!,
            color: const Color(AppConfig.successColor),
          ),

        if (_employee?.managerName != null) ...[
          const SizedBox(height: AppConfig.spacing),
          _buildInfoRow(
            icon: Icons.supervisor_account,
            label: 'المدير المباشر',
            value: _employee!.managerName!,
            color: const Color(0xFFFF9500),
          ),
        ],
      ],
    );
  }

  /// بناء صف معلومة واحدة
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConfig.smallSpacing),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppConfig.smallSpacing),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: AppConfig.spacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(AppConfig.secondaryTextColor),
                    fontSize: AppConfig.smallFontSize,
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: const Color(AppConfig.darkTextColor),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: const Color(AppConfig.whiteColor),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.cardShadowColor),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإجراءات السريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppConfig.darkTextColor),
            ),
          ),
          const SizedBox(height: AppConfig.spacing),
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.event_available,
                  label: 'أنواع الإجازات',
                  color: const Color(AppConfig.primaryColor),
                  onTap: () => setState(() => _currentIndex = 1),
                ),
              ),
              const SizedBox(width: AppConfig.spacing),
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.history,
                  label: 'طلباتي',
                  color: const Color(AppConfig.successColor),
                  onTap: () => setState(() => _currentIndex = 2),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConfig.spacing),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppConfig.spacing),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(height: AppConfig.smallSpacing),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatsCards() {
    return Container(
      padding: const EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: const Color(AppConfig.whiteColor),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.cardShadowColor),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نظرة عامة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppConfig.darkTextColor),
            ),
          ),
          const SizedBox(height: AppConfig.spacing),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.access_time,
                  label: 'ساعات العمل',
                  value: '8 ساعات',
                  color: const Color(AppConfig.primaryColor),
                ),
              ),
              const SizedBox(width: AppConfig.spacing),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.calendar_today,
                  label: 'أيام العمل',
                  value: '5 أيام',
                  color: const Color(AppConfig.successColor),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConfig.smallSpacing),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: const Color(AppConfig.secondaryTextColor),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(AppConfig.whiteColor),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConfig.largeBorderRadius),
          topRight: Radius.circular(AppConfig.largeBorderRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.cardShadowColor),
            blurRadius: 20,
            offset: const Offset(0, -10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppConfig.largeBorderRadius),
          topRight: Radius.circular(AppConfig.largeBorderRadius),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: const Color(AppConfig.whiteColor),
          selectedItemColor: const Color(AppConfig.primaryColor),
          unselectedItemColor: const Color(AppConfig.secondaryTextColor),
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: AppConfig.smallFontSize,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: AppConfig.smallFontSize,
          ),
          items: _buildNavigationItems(),
        ),
      ),
    );
  }

  /// معالجة النقر على التبويبات
  void _onTabTapped(int index) {
    // التأكد من أن الفهرس صحيح
    final maxIndex = _isLeaveManager ? 3 : 2;
    if (index > maxIndex) return;

    setState(() => _currentIndex = index);
  }

  /// الانتقال إلى شاشة الإعدادات
  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  /// بناء عناصر التنقل السفلي
  List<BottomNavigationBarItem> _buildNavigationItems() {
    final items = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.person),
        label: 'الملف الشخصي',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.event_available),
        label: 'أنواع الإجازات',
      ),
      const BottomNavigationBarItem(icon: Icon(Icons.history), label: 'طلباتي'),
    ];

    // إضافة تبويب الموافقة للمديرين فقط
    if (_isLeaveManager) {
      items.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.approval),
          label: 'الموافقات',
        ),
      );
    }

    return items;
  }

  /// بناء صورة الموظف
  Widget _buildEmployeeAvatar() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        boxShadow: [
          BoxShadow(
            color: const Color(AppConfig.primaryColor).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(50),
        child: _employee?.image1920 != null && _employee!.image1920!.isNotEmpty
            ? Image.memory(
                base64Decode(_employee!.image1920!),
                width: 100,
                height: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar();
                },
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// بناء الصورة الافتراضية
  Widget _buildDefaultAvatar() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(AppConfig.primaryColor), Color(0xFF2563EB)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(50),
      ),
      child: const Icon(
        Icons.person,
        size: 50,
        color: Color(AppConfig.whiteColor),
      ),
    );
  }
}
