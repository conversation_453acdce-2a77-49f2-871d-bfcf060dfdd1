import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/login_screen.dart';
import 'config/app_config.dart';
import 'providers/language_provider.dart';
import 'generated/l10n/app_localizations.dart';
import 'services/environment_service.dart';
import 'services/certificate_pinning_service.dart';
import 'services/auto_logout_service.dart';

void main() async {
  // تأكد من تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة الإعدادات الآمنة
  await AppConfig.initialize();

  // تهيئة Certificate Pinning
  await CertificatePinningService.initialize();

  // التحقق من الإعدادات وإظهار تحذير إذا لزم الأمر
  final warning = EnvironmentService.getDummyValuesWarning();
  if (warning != null) {
    debugPrint('⚠️ $warning');
  }

  // تعيين اتجاه شريط الحالة للتصميم المصرفي
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Color(AppConfig.whiteColor),
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(
    ChangeNotifierProvider(
      create: (context) => LanguageProvider(),
      child: const BankEmployeeApp(),
    ),
  );
}

/// التطبيق المصرفي الحديث لموظفي البنك
class BankEmployeeApp extends StatefulWidget {
  const BankEmployeeApp({super.key});

  /// مفتاح التنقل العام للتطبيق
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  State<BankEmployeeApp> createState() => _BankEmployeeAppState();
}

class _BankEmployeeAppState extends State<BankEmployeeApp> {
  @override
  void initState() {
    super.initState();
    // تهيئة اللغة المحفوظة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initialize();

      // تهيئة خدمة تسجيل الخروج التلقائي
      _initializeAutoLogoutService();
    });
  }

  /// تهيئة خدمة تسجيل الخروج التلقائي
  Future<void> _initializeAutoLogoutService() async {
    try {
      await AutoLogoutService.instance.initialize(BankEmployeeApp.navigatorKey);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ خطأ في تهيئة خدمة تسجيل الخروج التلقائي: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return MaterialApp(
          title: AppConfig.appName,
          debugShowCheckedModeBanner: false,
          navigatorKey: BankEmployeeApp.navigatorKey,

          // إعدادات الترجمة والمحلية
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar'), // العربية
            Locale('en'), // الإنجليزية
            Locale('fr'), // الفرنسية
          ],
          locale: languageProvider.locale, // اللغة المحفوظة

          theme: _buildBankTheme(),

          // الشاشة الرئيسية هي شاشة تسجيل الدخول
          home: const LoginScreen(),
        );
      },
    );
  }

  /// بناء تصميم مصرفي حديث ومتطور
  ThemeData _buildBankTheme() {
    return ThemeData(
      // تصميم التطبيق المصرفي
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(AppConfig.primaryColor),
        brightness: Brightness.light,
        primary: const Color(AppConfig.primaryColor),
        surface: const Color(AppConfig.whiteColor),
        error: const Color(AppConfig.errorColor),
        onPrimary: const Color(AppConfig.whiteColor),
        onSurface: const Color(AppConfig.darkTextColor),
      ),
      scaffoldBackgroundColor: const Color(AppConfig.lightGrayColor),
      useMaterial3: true,

      // خط حديث ونظيف
      fontFamily: 'Roboto',

      // تخصيص النصوص
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: AppConfig.headlineFontSize,
          fontWeight: FontWeight.bold,
          color: Color(AppConfig.darkTextColor),
          letterSpacing: -0.5,
        ),
        titleLarge: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.w600,
          color: Color(AppConfig.darkTextColor),
        ),
        bodyLarge: TextStyle(
          fontSize: AppConfig.bodyFontSize,
          fontWeight: FontWeight.normal,
          color: Color(AppConfig.darkTextColor),
        ),
        bodyMedium: TextStyle(
          fontSize: AppConfig.captionFontSize,
          color: Color(AppConfig.secondaryTextColor),
        ),
        labelSmall: TextStyle(
          fontSize: AppConfig.smallFontSize,
          color: Color(AppConfig.secondaryTextColor),
        ),
      ),

      // تخصيص الأزرار المرتفعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(AppConfig.primaryColor),
          foregroundColor: const Color(AppConfig.whiteColor),
          elevation: AppConfig.cardElevation,
          shadowColor: const Color(AppConfig.cardShadowColor),
          minimumSize: const Size(double.infinity, AppConfig.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          textStyle: const TextStyle(
            fontSize: AppConfig.bodyFontSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // تخصيص الأزرار المحددة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(AppConfig.primaryColor),
          side: const BorderSide(
            color: Color(AppConfig.primaryColor),
            width: 2,
          ),
          minimumSize: const Size(double.infinity, AppConfig.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          textStyle: const TextStyle(
            fontSize: AppConfig.bodyFontSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // تخصيص البطاقات
      cardTheme: CardThemeData(
        elevation: AppConfig.cardElevation,
        shadowColor: const Color(AppConfig.cardShadowColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        color: const Color(AppConfig.whiteColor),
        margin: const EdgeInsets.all(AppConfig.smallSpacing),
      ),

      // تخصيص حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(AppConfig.whiteColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: const BorderSide(color: Color(AppConfig.dividerColor)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: const BorderSide(color: Color(AppConfig.dividerColor)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: const BorderSide(
            color: Color(AppConfig.primaryColor),
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: const BorderSide(color: Color(AppConfig.errorColor)),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConfig.spacing,
          vertical: AppConfig.spacing,
        ),
        hintStyle: const TextStyle(
          color: Color(AppConfig.secondaryTextColor),
          fontSize: AppConfig.bodyFontSize,
        ),
      ),

      // تخصيص شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(AppConfig.whiteColor),
        foregroundColor: Color(AppConfig.darkTextColor),
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.bold,
          color: Color(AppConfig.darkTextColor),
        ),
        iconTheme: IconThemeData(
          color: Color(AppConfig.darkTextColor),
          size: 24,
        ),
      ),

      // تخصيص الفواصل
      dividerTheme: const DividerThemeData(
        color: Color(AppConfig.dividerColor),
        thickness: 1,
        space: 1,
      ),

      // تخصيص الأيقونات
      iconTheme: const IconThemeData(
        color: Color(AppConfig.primaryColor),
        size: 24,
      ),
    );
  }
}
