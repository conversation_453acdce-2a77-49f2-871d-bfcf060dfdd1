import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/services/environment_service.dart';

void main() {
  group('EnvironmentService Tests', () {
    setUp(() async {
      // إعادة تعيين الخدمة قبل كل اختبار
      await EnvironmentService.reload();
    });

    test('should initialize environment service', () async {
      // تنفيذ
      await EnvironmentService.initialize();

      // تحقق - لا يجب أن يحدث خطأ
      expect(true, isTrue);
    });

    test(
      'should get default server URL when no environment variables',
      () async {
        // تنفيذ
        await EnvironmentService.initialize();
        final serverUrl = EnvironmentService.getServerUrl();

        // تحقق - يجب أن يرجع القيمة الافتراضية
        expect(serverUrl, isNotEmpty);
        expect(serverUrl, contains('http'));
      },
    );

    test('should get default database when no environment variables', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final database = EnvironmentService.getDatabase();

      // تحقق - يجب أن يرجع القيمة الافتراضية
      expect(database, isNotEmpty);
    });

    test('should have API key functionality', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final apiKey = EnvironmentService.getApiKey();

      // تحقق - يجب أن يكون string (قد يكون فارغاً)
      expect(apiKey, isA<String>());
    });

    test('should validate configuration', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final isValid = EnvironmentService.validateConfiguration();

      // تحقق - الإعدادات الافتراضية يجب أن تكون صحيحة
      expect(isValid, isTrue);
    });

    test('should get all settings', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final settings = EnvironmentService.getAllSettings();

      // تحقق
      expect(settings, isA<Map<String, String>>());
      expect(settings.containsKey('serverUrl'), isTrue);
      expect(settings.containsKey('database'), isTrue);
      expect(settings.containsKey('apiKey'), isTrue);
    });

    test('should handle reload correctly', () async {
      // ترتيب
      await EnvironmentService.initialize();
      final originalUrl = EnvironmentService.getServerUrl();

      // تنفيذ
      await EnvironmentService.reload();
      final newUrl = EnvironmentService.getServerUrl();

      // تحقق - يجب أن تعمل بعد إعادة التحميل
      expect(newUrl, equals(originalUrl));
    });

    test('should get API key (required)', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final apiKey = EnvironmentService.getApiKey();

      // تحقق - يجب أن يكون string (مطلوب الآن)
      expect(apiKey, isA<String>());
    });

    test('should check if API key exists', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final hasApiKey = EnvironmentService.hasApiKey();

      // تحقق - يجب أن يكون boolean
      expect(hasApiKey, isA<bool>());
    });

    test('should get all settings', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final settings = EnvironmentService.getAllSettings();

      // تحقق
      expect(settings, isA<Map<String, String>>());
      expect(settings.containsKey('serverUrl'), isTrue);
      expect(settings.containsKey('database'), isTrue);
      expect(settings.containsKey('apiKey'), isTrue);
    });

    test('should throw exception when not initialized', () async {
      // إعادة تعيين الخدمة
      await EnvironmentService.reload();

      // محاولة الوصول بدون تهيئة (لكن reload يقوم بالتهيئة تلقائياً)
      // لذا سنتحقق من أن الخدمة تعمل
      final serverUrl = EnvironmentService.getServerUrl();
      expect(serverUrl, isNotEmpty);
    });

    test('should handle configuration loading', () async {
      // تنفيذ
      await EnvironmentService.initialize();
      final serverUrl = EnvironmentService.getServerUrl();
      final database = EnvironmentService.getDatabase();

      // تحقق
      expect(serverUrl, isNotEmpty);
      expect(database, isNotEmpty);
    });

    test('should handle invalid configuration gracefully', () async {
      // تنفيذ
      await EnvironmentService.initialize();

      // تحقق - يجب أن يعمل حتى مع إعدادات غير صحيحة
      final isValid = EnvironmentService.validateConfiguration();
      expect(isValid, isA<bool>());
    });

    test('should maintain configuration cache', () async {
      // تنفيذ
      await EnvironmentService.initialize();

      final url1 = EnvironmentService.getServerUrl();
      final url2 = EnvironmentService.getServerUrl();

      // تحقق - يجب أن ترجع نفس القيمة (من الكاش)
      expect(url1, equals(url2));
    });

    test('should handle multiple initializations', () async {
      // تنفيذ - تهيئة متعددة
      await EnvironmentService.initialize();
      await EnvironmentService.initialize();
      await EnvironmentService.initialize();

      // تحقق - يجب أن تعمل بدون مشاكل
      final serverUrl = EnvironmentService.getServerUrl();
      expect(serverUrl, isNotEmpty);
    });
  });
}
