import 'package:flutter_test/flutter_test.dart';
import 'package:odoo_employee_app/services/certificate_pinning_service.dart';
import 'package:odoo_employee_app/services/environment_service.dart';

void main() {
  // تهيئة Flutter binding للاختبارات
  TestWidgetsFlutterBinding.ensureInitialized();

  group('CertificatePinningService Tests', () {
    setUp(() async {
      // إعادة تعيين الخدمة قبل كل اختبار
      CertificatePinningService.reset();

      // تهيئة خدمة البيئة للاختبارات
      await EnvironmentService.initialize();
    });

    test('should initialize service successfully', () async {
      // تنفيذ
      await CertificatePinningService.initialize();

      // تحقق
      expect(CertificatePinningService.isInitialized, isTrue);
    });

    test('should handle initialization with invalid server URL', () async {
      // تنفيذ - سيتم التعامل مع URL غير صحيح بأمان
      await CertificatePinningService.initialize();

      // تحقق - يجب أن تكتمل التهيئة حتى مع URL غير صحيح
      expect(CertificatePinningService.isInitialized, isTrue);
    });

    test('should add pinned certificate manually', () {
      // ترتيب
      const testFingerprint =
          'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99';

      // تنفيذ
      CertificatePinningService.addPinnedCertificate(testFingerprint);

      // تحقق
      expect(CertificatePinningService.getPinnedCertificatesCount(), equals(1));
    });

    test('should not add duplicate certificates', () {
      // ترتيب
      const testFingerprint =
          'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99';

      // تنفيذ
      CertificatePinningService.addPinnedCertificate(testFingerprint);
      CertificatePinningService.addPinnedCertificate(
        testFingerprint,
      ); // نفس الشهادة

      // تحقق
      expect(CertificatePinningService.getPinnedCertificatesCount(), equals(1));
    });

    test('should clear all pinned certificates', () {
      // ترتيب
      const testFingerprint1 =
          'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99';
      const testFingerprint2 =
          'BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA';

      CertificatePinningService.addPinnedCertificate(testFingerprint1);
      CertificatePinningService.addPinnedCertificate(testFingerprint2);

      // تنفيذ
      CertificatePinningService.clearPinnedCertificates();

      // تحقق
      expect(CertificatePinningService.getPinnedCertificatesCount(), equals(0));
    });

    test(
      'should handle certificate pinning check with no certificates',
      () async {
        // ترتيب
        await CertificatePinningService.initialize();

        // تحقق - يجب أن تكون الخدمة مهيأة
        expect(CertificatePinningService.isInitialized, isTrue);

        // تحقق - يجب أن تكون هناك شهادات مثبتة بعد التهيئة
        expect(
          CertificatePinningService.getPinnedCertificatesCount(),
          greaterThan(0),
        );
      },
    );

    test('should handle certificate pinning check with invalid URL', () async {
      // ترتيب
      await CertificatePinningService.initialize();

      // إضافة شهادة وهمية لتفعيل Certificate Pinning
      const testFingerprint =
          'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99';
      CertificatePinningService.addPinnedCertificate(testFingerprint);

      // تحقق - يجب أن تكون الشهادة مضافة
      expect(
        CertificatePinningService.getPinnedCertificatesCount(),
        greaterThan(1),
      );
    });

    test('should reset service correctly', () {
      // ترتيب
      const testFingerprint =
          'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99';
      CertificatePinningService.addPinnedCertificate(testFingerprint);

      // تنفيذ
      CertificatePinningService.reset();

      // تحقق
      expect(CertificatePinningService.isInitialized, isFalse);
      expect(CertificatePinningService.getPinnedCertificatesCount(), equals(0));
    });

    test('should handle multiple initialization calls safely', () async {
      // تنفيذ
      await CertificatePinningService.initialize();
      await CertificatePinningService.initialize(); // استدعاء ثاني
      await CertificatePinningService.initialize(); // استدعاء ثالث

      // تحقق - يجب أن تبقى الخدمة مهيأة بشكل صحيح
      expect(CertificatePinningService.isInitialized, isTrue);
    });

    test('should get pinned certificates in debug mode only', () {
      // ترتيب
      const testFingerprint =
          'AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99';
      CertificatePinningService.addPinnedCertificate(testFingerprint);

      // تنفيذ
      final certificates = CertificatePinningService.getPinnedCertificates();

      // تحقق - في وضع الاختبار (debug mode)، يجب إرجاع القائمة
      expect(certificates, isA<List<String>>());
    });

    test('should handle certificate pinning when disabled in config', () async {
      // ملاحظة: هذا الاختبار يفترض أن AppConfig.enableCertificatePinning = false
      // في التطبيق الحقيقي، يمكن تعديل هذا الإعداد ديناميكياً

      // ترتيب
      await CertificatePinningService.initialize();
      const testUrl = 'https://example.com';

      // تنفيذ
      final result = await CertificatePinningService.checkCertificatePinning(
        testUrl,
      );

      // تحقق - النتيجة تعتمد على إعدادات التطبيق
      expect(result, isA<bool>());
    });
  });

  group('CertificatePinningService Integration Tests', () {
    test(
      'should work with real HTTPS connection (integration test)',
      () async {
        // ملاحظة: هذا اختبار تكامل يتطلب اتصال إنترنت
        // يمكن تخطيه في بيئة CI/CD إذا لزم الأمر

        // ترتيب
        await CertificatePinningService.initialize();

        // استخدام خادم آمن معروف للاختبار
        const testUrl = 'https://www.google.com';

        // تنفيذ
        final result = await CertificatePinningService.checkCertificatePinning(
          testUrl,
        );

        // تحقق - يجب أن يعمل مع خادم حقيقي
        expect(result, isA<bool>());
      },
      skip: 'Integration test - requires internet connection',
    );
  });
}
