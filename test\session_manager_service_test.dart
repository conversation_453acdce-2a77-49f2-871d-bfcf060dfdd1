import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:odoo_employee_app/services/session_manager_service.dart';
import 'package:odoo_employee_app/services/environment_service.dart';

void main() {
  // تهيئة Flutter binding للاختبارات
  TestWidgetsFlutterBinding.ensureInitialized();

  group('SessionManagerService Tests', () {
    setUp(() async {
      // إعداد SharedPreferences للاختبارات
      SharedPreferences.setMockInitialValues({});

      // تهيئة خدمة البيئة للاختبارات
      await EnvironmentService.initialize();

      // إعادة تعيين الخدمة قبل كل اختبار
      await SessionManagerService.instance.endSession();
    });

    test('should initialize service successfully', () async {
      // تنفيذ
      await SessionManagerService.instance.initialize();

      // تحقق
      expect(SessionManagerService.instance, isNotNull);
    });

    test('should start session with valid user ID', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      const userId = 123;

      // تنفيذ
      await SessionManagerService.instance.startSession(userId);

      // تحقق
      final sessionInfo = SessionManagerService.instance.getSessionInfo();
      expect(sessionInfo['isActive'], isTrue);
      expect(sessionInfo['userId'], equals(userId));
      expect(sessionInfo['startTime'], isNotNull);
      expect(sessionInfo['lastActivity'], isNotNull);
    });

    test('should record user activity', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // الحصول على وقت النشاط الأولي
      final initialInfo = SessionManagerService.instance.getSessionInfo();
      final initialActivity = initialInfo['lastActivity'];

      // انتظار قصير لضمان تغيير الوقت
      await Future.delayed(const Duration(milliseconds: 10));

      // تنفيذ
      await SessionManagerService.instance.recordActivity();

      // تحقق
      final updatedInfo = SessionManagerService.instance.getSessionInfo();
      final updatedActivity = updatedInfo['lastActivity'];

      expect(updatedActivity, isNot(equals(initialActivity)));
    });

    test('should validate active session', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // تنفيذ
      final isValid = SessionManagerService.instance.isSessionValid();

      // تحقق
      expect(isValid, isTrue);
    });

    test('should invalidate expired session', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // محاكاة انتهاء الجلسة بتعديل وقت النشاط الأخير
      // (هذا اختبار مفاهيمي - في التطبيق الحقيقي ستنتهي الجلسة بعد المهلة المحددة)
      await SessionManagerService.instance.endSession();

      // تنفيذ
      final isValid = SessionManagerService.instance.isSessionValid();

      // تحقق
      expect(isValid, isFalse);
    });

    test('should end session successfully', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // تنفيذ
      await SessionManagerService.instance.endSession();

      // تحقق
      final sessionInfo = SessionManagerService.instance.getSessionInfo();
      expect(sessionInfo['isActive'], isFalse);
      expect(sessionInfo['userId'], isNull);
      expect(sessionInfo['startTime'], isNull);
      expect(sessionInfo['lastActivity'], isNull);
    });

    test('should get correct remaining minutes for active session', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // تنفيذ
      final remainingMinutes = SessionManagerService.instance
          .getRemainingMinutes();

      // تحقق
      expect(remainingMinutes, greaterThan(0));
      expect(
        remainingMinutes,
        lessThanOrEqualTo(
          SessionManagerService.instance.getSessionTimeoutMinutes(),
        ),
      );
    });

    test('should return zero remaining minutes for inactive session', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      // لا نبدأ جلسة

      // تنفيذ
      final remainingMinutes = SessionManagerService.instance
          .getRemainingMinutes();

      // تحقق
      expect(remainingMinutes, equals(0));
    });

    test('should get session timeout from configuration', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();

      // تنفيذ
      final timeoutMinutes = SessionManagerService.instance
          .getSessionTimeoutMinutes();

      // تحقق
      expect(timeoutMinutes, isA<int>());
      expect(timeoutMinutes, greaterThan(0));
    });

    test('should get warning minutes from configuration', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();

      // تنفيذ
      final warningMinutes = SessionManagerService.instance.getWarningMinutes();

      // تحقق
      expect(warningMinutes, isA<int>());
      expect(warningMinutes, greaterThan(0));
    });

    test('should handle session callbacks', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      bool activityCalled = false;

      SessionManagerService.instance.setOnSessionWarning(() {
        // تم استدعاء تنبيه انتهاء الجلسة
      });

      SessionManagerService.instance.setOnSessionExpired(() {
        // تم استدعاء انتهاء الجلسة
      });

      SessionManagerService.instance.setOnActivityDetected(() {
        activityCalled = true;
      });

      // تنفيذ
      await SessionManagerService.instance.startSession(123);
      await SessionManagerService.instance.recordActivity();

      // تحقق
      expect(activityCalled, isTrue);
      // ملاحظة: warningCalled و expiredCalled لن يتم استدعاؤهما في الاختبار
      // لأنهما يعتمدان على المؤقتات الزمنية
    });

    test('should provide complete session info', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // تنفيذ
      final sessionInfo = SessionManagerService.instance.getSessionInfo();

      // تحقق
      expect(sessionInfo, isA<Map<String, dynamic>>());
      expect(sessionInfo.containsKey('isActive'), isTrue);
      expect(sessionInfo.containsKey('userId'), isTrue);
      expect(sessionInfo.containsKey('startTime'), isTrue);
      expect(sessionInfo.containsKey('lastActivity'), isTrue);
      expect(sessionInfo.containsKey('remainingMinutes'), isTrue);
      expect(sessionInfo.containsKey('timeoutMinutes'), isTrue);
      expect(sessionInfo.containsKey('warningMinutes'), isTrue);
    });

    test('should handle multiple session starts correctly', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();

      // تنفيذ
      await SessionManagerService.instance.startSession(123);
      await SessionManagerService.instance.startSession(456); // جلسة جديدة

      // تحقق
      final sessionInfo = SessionManagerService.instance.getSessionInfo();
      expect(sessionInfo['isActive'], isTrue);
      expect(sessionInfo['userId'], equals(456)); // المستخدم الجديد
    });

    test('should handle activity recording for inactive session', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      // لا نبدأ جلسة

      // تنفيذ - يجب ألا يسبب خطأ
      await SessionManagerService.instance.recordActivity();

      // تحقق - لا يجب أن تكون هناك جلسة نشطة
      final sessionInfo = SessionManagerService.instance.getSessionInfo();
      expect(sessionInfo['isActive'], isFalse);
    });

    test('should dispose service correctly', () async {
      // ترتيب
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // تنفيذ
      SessionManagerService.instance.dispose();

      // تحقق - يجب أن تعمل بدون أخطاء
      expect(() => SessionManagerService.instance.dispose(), returnsNormally);
    });
  });

  group('SessionManagerService Integration Tests', () {
    test('should persist session data across restarts', () async {
      // ترتيب - إنشاء جلسة
      await SessionManagerService.instance.initialize();
      await SessionManagerService.instance.startSession(123);

      // محاكاة إعادة تشغيل التطبيق
      await SessionManagerService.instance.endSession();

      // تنفيذ - تهيئة جديدة
      await SessionManagerService.instance.initialize();

      // تحقق - يجب أن تكون الجلسة منتهية بعد إعادة التشغيل
      final newInfo = SessionManagerService.instance.getSessionInfo();
      expect(newInfo['isActive'], isFalse);
    });
  });
}
