import 'package:flutter/material.dart';
import '../services/odoo_service.dart';
import '../models/pending_leave_request.dart';
import '../config/app_config.dart';

/// شاشة الموافقة على إجازات الموظفين للمديرين
class LeaveApprovalScreen extends StatefulWidget {
  final OdooService odooService;
  final int uid;
  final String password;

  const LeaveApprovalScreen({
    super.key,
    required this.odooService,
    required this.uid,
    required this.password,
  });

  @override
  State<LeaveApprovalScreen> createState() => _LeaveApprovalScreenState();
}

class _LeaveApprovalScreenState extends State<LeaveApprovalScreen>
    with TickerProviderStateMixin {
  List<PendingLeaveRequest> _allRequests = [];
  List<PendingLeaveRequest> _filteredRequests = [];
  bool _isLoading = true;
  String? _errorMessage;

  // التبويبات والتصفية
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedLeaveType = 'الكل';

  // إحصائيات
  int _pendingCount = 0;
  int _approvedCount = 0;
  int _rejectedCount = 0;

  // حالة البطاقات المتوسعة
  final Set<int> _expandedCards = <int>{};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_onTabChanged);
    _searchController.addListener(_onSearchChanged);
    _loadPendingRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل طلبات الإجازة المعلقة
  Future<void> _loadPendingRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final requestsData = await widget.odooService.getPendingLeaveRequests(
        uid: widget.uid,
        password: widget.password,
      );

      setState(() {
        _allRequests = requestsData
            .map((data) => PendingLeaveRequest.fromOdooData(data))
            .toList();
        _calculateStatistics();
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// إعادة تحميل البيانات
  Future<void> _refreshData() async {
    await _loadPendingRequests();
  }

  /// حساب الإحصائيات
  void _calculateStatistics() {
    _pendingCount = _allRequests.where((r) => r.state == 'confirm').length;
    _approvedCount = _allRequests
        .where((r) => r.state == 'validate' || r.state == 'validate1')
        .length;
    _rejectedCount = _allRequests.where((r) => r.state == 'refuse').length;
  }

  /// تطبيق التصفية والبحث
  void _applyFilters() {
    List<PendingLeaveRequest> filtered = List.from(_allRequests);

    // تصفية حسب التبويب المختار
    switch (_tabController.index) {
      case 0: // الكل
        break;
      case 1: // معلقة
        filtered = filtered.where((r) => r.state == 'confirm').toList();
        break;
      case 2: // موافق عليها
        filtered = filtered
            .where((r) => r.state == 'validate' || r.state == 'validate1')
            .toList();
        break;
      case 3: // مرفوضة
        filtered = filtered.where((r) => r.state == 'refuse').toList();
        break;
    }

    // تصفية حسب البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (r) =>
                r.employeeName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                r.leaveTypeName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ),
          )
          .toList();
    }

    // تصفية حسب نوع الإجازة
    if (_selectedLeaveType != 'الكل') {
      filtered = filtered
          .where((r) => r.leaveTypeName == _selectedLeaveType)
          .toList();
    }

    _filteredRequests = filtered;
  }

  /// عند تغيير التبويب
  void _onTabChanged() {
    setState(() {
      _applyFilters();
    });
  }

  /// عند تغيير البحث
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _applyFilters();
    });
  }

  /// الموافقة على طلب إجازة
  Future<void> _approveRequest(PendingLeaveRequest request) async {
    final confirmed = await _showConfirmationDialog(
      title: 'الموافقة على الإجازة',
      message: 'هل تريد الموافقة على إجازة ${request.employeeName}؟',
      confirmText: 'موافق',
      confirmColor: Colors.green,
    );

    if (confirmed == true) {
      final success = await widget.odooService.approveLeaveRequest(
        uid: widget.uid,
        password: widget.password,
        leaveId: request.id,
      );

      if (success) {
        _showSnackBar('تم الموافقة على الإجازة بنجاح', Colors.green);
        _refreshData();
      } else {
        _showSnackBar('فشل في الموافقة على الإجازة', Colors.red);
      }
    }
  }

  /// رفض طلب إجازة
  Future<void> _rejectRequest(PendingLeaveRequest request) async {
    final confirmed = await _showConfirmationDialog(
      title: 'رفض الإجازة',
      message: 'هل تريد رفض إجازة ${request.employeeName}؟',
      confirmText: 'رفض',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      final success = await widget.odooService.rejectLeaveRequest(
        uid: widget.uid,
        password: widget.password,
        leaveId: request.id,
      );

      if (success) {
        _showSnackBar('تم رفض الإجازة', Colors.orange);
        _refreshData();
      } else {
        _showSnackBar('فشل في رفض الإجازة', Colors.red);
      }
    }
  }

  /// عرض نافذة تأكيد
  Future<bool?> _showConfirmationDialog({
    required String title,
    required String message,
    required String confirmText,
    required Color confirmColor,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message), backgroundColor: color));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(AppConfig.lightGrayColor),
      appBar: _buildAppBar(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        color: const Color(AppConfig.primaryColor),
        child: _buildBody(),
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: const Color(AppConfig.primaryColor),
      foregroundColor: const Color(AppConfig.whiteColor),
      title: const Text(
        'إدارة إجازات الموظفين',
        style: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_allRequests.isEmpty) {
      return _buildEmptyState();
    }

    return _buildMainContent();
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Color(AppConfig.primaryColor)),
          SizedBox(height: AppConfig.spacing),
          Text(
            'جاري تحميل إجازات الموظفين...',
            style: TextStyle(
              color: Color(AppConfig.secondaryTextColor),
              fontSize: AppConfig.bodyFontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.largeSpacing),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Color(AppConfig.errorColor),
            ),
            const SizedBox(height: AppConfig.spacing),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: const Color(AppConfig.darkTextColor),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.smallSpacing),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Color(AppConfig.secondaryTextColor),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.largeSpacing),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة عدم وجود طلبات
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.largeSpacing),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Color(AppConfig.successColor),
            ),
            const SizedBox(height: AppConfig.spacing),
            Text(
              'لا توجد طلبات معلقة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: const Color(AppConfig.darkTextColor),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.smallSpacing),
            const Text(
              'لا توجد طلبات إجازة من الموظفين تحت إدارتك',
              style: TextStyle(color: Color(AppConfig.secondaryTextColor)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الإحصائيات
  Widget _buildStatisticsCard() {
    return Container(
      margin: const EdgeInsets.all(AppConfig.spacing),
      padding: const EdgeInsets.all(AppConfig.largeSpacing),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667EEA).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.pending_actions,
              count: _pendingCount,
              label: 'معلقة',
              color: Colors.orange,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.check_circle,
              count: _approvedCount,
              label: 'موافق عليها',
              color: Colors.green,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.cancel,
              count: _rejectedCount,
              label: 'مرفوضة',
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث والتصفية
  Widget _buildSearchAndFilter() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConfig.spacing),
      padding: const EdgeInsets.all(AppConfig.spacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // شريط البحث
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث بالاسم أو نوع الإجازة...',
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: const Color(AppConfig.lightGrayColor),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.spacing,
                  vertical: 12,
                ),
              ),
            ),
          ),

          const SizedBox(width: AppConfig.spacing),

          // زر التصفية
          Container(
            decoration: BoxDecoration(
              color: const Color(AppConfig.primaryColor),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            ),
            child: IconButton(
              onPressed: _showFilterDialog,
              icon: const Icon(Icons.filter_list, color: Colors.white),
              tooltip: 'تصفية',
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار التصفية
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية الطلبات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedLeaveType,
              decoration: const InputDecoration(
                labelText: 'نوع الإجازة',
                border: OutlineInputBorder(),
              ),
              items: _getLeaveTypes().map((type) {
                return DropdownMenuItem(value: type, child: Text(type));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLeaveType = value ?? 'الكل';
                  _applyFilters();
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedLeaveType = 'الكل';
                _searchController.clear();
                _searchQuery = '';
                _applyFilters();
              });
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أنواع الإجازات المتاحة
  List<String> _getLeaveTypes() {
    Set<String> types = {'الكل'};
    for (var request in _allRequests) {
      types.add(request.leaveTypeName);
    }
    return types.toList();
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConfig.spacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(AppConfig.primaryColor),
        unselectedLabelColor: Colors.grey,
        indicatorColor: const Color(AppConfig.primaryColor),
        indicatorWeight: 3,
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.list, size: 16),
                const SizedBox(width: 2),
                Flexible(
                  child: Text(
                    'الكل (${_allRequests.length})',
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.pending_actions, size: 16),
                const SizedBox(width: 2),
                Flexible(
                  child: Text(
                    'معلقة ($_pendingCount)',
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check_circle, size: 16),
                const SizedBox(width: 2),
                Flexible(
                  child: Text(
                    'موافق ($_approvedCount)',
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.cancel, size: 16),
                const SizedBox(width: 2),
                Flexible(
                  child: Text(
                    'مرفوضة ($_rejectedCount)',
                    style: const TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent() {
    return Column(
      children: [
        // إحصائيات سريعة
        _buildStatisticsCard(),

        // شريط البحث والتصفية
        _buildSearchAndFilter(),

        // التبويبات
        _buildTabBar(),

        // قائمة الطلبات
        Expanded(child: _buildRequestsList()),
      ],
    );
  }

  /// بناء قائمة الطلبات
  Widget _buildRequestsList() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildRequestsListView(_allRequests), // الكل
        _buildRequestsListView(
          _allRequests.where((r) => r.state == 'confirm').toList(),
        ), // معلقة
        _buildRequestsListView(
          _allRequests
              .where((r) => r.state == 'validate' || r.state == 'validate1')
              .toList(),
        ), // موافق عليها
        _buildRequestsListView(
          _allRequests.where((r) => r.state == 'refuse').toList(),
        ), // مرفوضة
      ],
    );
  }

  /// بناء عرض قائمة الطلبات
  Widget _buildRequestsListView(List<PendingLeaveRequest> requests) {
    if (requests.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد طلبات في هذا القسم',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConfig.spacing),
      itemCount: _filteredRequests.length,
      itemBuilder: (context, index) {
        final request = _filteredRequests[index];
        return _buildCompactRequestCard(request, index);
      },
    );
  }

  /// بناء البطاقة المضغوطة القابلة للتوسيع
  Widget _buildCompactRequestCard(PendingLeaveRequest request, int index) {
    final isExpanded = _expandedCards.contains(index);

    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.spacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // الجزء المضغوط (دائماً مرئي)
          InkWell(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedCards.remove(index);
                } else {
                  _expandedCards.add(index);
                }
              });
            },
            borderRadius: BorderRadius.circular(AppConfig.largeBorderRadius),
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.largeSpacing),
              child: Row(
                children: [
                  // أيقونة الحالة
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(request.stateColor).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Color(request.stateColor),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _getStateIcon(request.state),
                      color: Color(request.stateColor),
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: AppConfig.spacing),

                  // معلومات أساسية
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          request.employeeName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          request.leaveTypeName,
                          style: TextStyle(
                            color: const Color(AppConfig.primaryColor),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          request.dateRange,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // عدد الأيام وسهم التوسيع
                  Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(
                            AppConfig.primaryColor,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          request.daysText,
                          style: const TextStyle(
                            color: Color(AppConfig.primaryColor),
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        color: Colors.grey,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // الجزء الموسع (يظهر عند النقر)
          if (isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(AppConfig.largeSpacing),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تفاصيل إضافية
                  if (request.description != null &&
                      request.description!.isNotEmpty) ...[
                    const Text(
                      'الوصف:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      request.description!,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: AppConfig.spacing),
                  ],

                  // أزرار الإجراء
                  _buildActionButtons(request),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء أزرار الإجراء حسب حالة الطلب
  Widget _buildActionButtons(PendingLeaveRequest request) {
    // إذا كان الطلب معلق، عرض أزرار الموافقة والرفض
    if (request.state == 'confirm') {
      return Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _rejectRequest(request),
              icon: const Icon(Icons.close, size: 18),
              label: const Text('رفض'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(AppConfig.errorColor),
                foregroundColor: const Color(AppConfig.whiteColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
              ),
            ),
          ),
          const SizedBox(width: AppConfig.spacing),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _approveRequest(request),
              icon: const Icon(Icons.check, size: 18),
              label: const Text('موافق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(AppConfig.successColor),
                foregroundColor: const Color(AppConfig.whiteColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
              ),
            ),
          ),
        ],
      );
    }

    // إذا كان الطلب معتمد أو مرفوض، عرض رسالة الحالة فقط
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Color(request.stateColor).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(color: Color(request.stateColor), width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getStateIcon(request.state),
            color: Color(request.stateColor),
            size: 20,
          ),
          const SizedBox(width: AppConfig.smallSpacing),
          Text(
            _getStateMessage(request.state),
            style: TextStyle(
              color: Color(request.stateColor),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStateIcon(String state) {
    switch (state) {
      case 'validate1':
        return Icons.thumb_up; // موافقة أولى
      case 'validate':
        return Icons.check_circle; // معتمدة نهائياً
      case 'refuse':
        return Icons.cancel; // مرفوضة
      default:
        return Icons.help_outline;
    }
  }

  /// الحصول على رسالة الحالة
  String _getStateMessage(String state) {
    switch (state) {
      case 'validate1':
        return 'تم الموافقة - في انتظار الاعتماد النهائي';
      case 'validate':
        return 'تم الاعتماد النهائي للطلب';
      case 'refuse':
        return 'تم رفض الطلب';
      default:
        return 'حالة غير معروفة';
    }
  }
}
